"""
Unit tests for Phase 3a logging infrastructure implementation.

Tests verify:
1. 100% backward compatibility with existing debug() function calls
2. SCRIPTWEAVER_DEBUG environment variable behavior preservation
3. New centralized logging functionality
4. Integration with existing AI logging infrastructure
5. Performance characteristics and error handling

Phase 3a Implementation - Comprehensive Testing
"""

import os
import sys
import unittest
import logging
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules under test
from debug_utils import debug, get_debug_mode, DEBUG_MODE
from core.logging_config import GretahLoggingManager, get_stage_logger, get_component_logger, is_debug_enabled


class TestBackwardCompatibility(unittest.TestCase):
    """Test backward compatibility with existing debug functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Reset environment variables
        if 'SCRIPTWEAVER_DEBUG' in os.environ:
            del os.environ['SCRIPTWEAVER_DEBUG']
        if 'GRETAH_LOG_LEVEL' in os.environ:
            del os.environ['GRETAH_LOG_LEVEL']
    
    @patch('builtins.print')
    def test_legacy_debug_call_with_scriptweaver_debug_false(self, mock_print):
        """Test that legacy debug() calls work when SCRIPTWEAVER_DEBUG=false."""
        os.environ['SCRIPTWEAVER_DEBUG'] = 'false'
        
        # Import fresh to pick up environment variable
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        # Call debug with legacy signature
        debug_utils.debug("Test message")
        
        # Verify console output is NOT printed when DEBUG_MODE is false
        mock_print.assert_not_called()
    
    @patch('builtins.print')
    def test_legacy_debug_call_with_scriptweaver_debug_true(self, mock_print):
        """Test that legacy debug() calls work when SCRIPTWEAVER_DEBUG=true."""
        os.environ['SCRIPTWEAVER_DEBUG'] = 'true'
        
        # Import fresh to pick up environment variable
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        # Call debug with legacy signature
        debug_utils.debug("Test message")
        
        # Verify console output is printed when DEBUG_MODE is true
        mock_print.assert_called_once_with("DEBUG: Test message")
    
    def test_debug_mode_flag_compatibility(self):
        """Test that DEBUG_MODE flag works exactly as before."""
        # Test with SCRIPTWEAVER_DEBUG=true
        os.environ['SCRIPTWEAVER_DEBUG'] = 'true'
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        self.assertTrue(debug_utils.DEBUG_MODE)
        self.assertTrue(debug_utils.get_debug_mode())
        
        # Test with SCRIPTWEAVER_DEBUG=false
        os.environ['SCRIPTWEAVER_DEBUG'] = 'false'
        importlib.reload(debug_utils)
        
        self.assertFalse(debug_utils.DEBUG_MODE)
    
    def test_debug_function_signature_compatibility(self):
        """Test that existing debug() function calls work unchanged."""
        # These calls should work without any errors
        try:
            debug("Simple message")
            debug("Message with variable: {}".format("test"))
            debug(f"F-string message: {'test'}")
        except Exception as e:
            self.fail(f"Legacy debug calls failed: {e}")


class TestCentralizedLogging(unittest.TestCase):
    """Test new centralized logging functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Reset environment variables
        for var in ['SCRIPTWEAVER_DEBUG', 'GRETAH_LOG_LEVEL', 'GRETAH_LOG_CONSOLE']:
            if var in os.environ:
                del os.environ[var]
    
    def test_gretah_logging_manager_singleton(self):
        """Test that GretahLoggingManager is a proper singleton."""
        manager1 = GretahLoggingManager.get_instance()
        manager2 = GretahLoggingManager.get_instance()
        
        self.assertIs(manager1, manager2)
    
    def test_stage_logger_creation(self):
        """Test creation of stage-specific loggers."""
        logger = get_stage_logger("stage10")
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, "gretah.scriptweaver.stage10")
    
    def test_component_logger_creation(self):
        """Test creation of component-specific loggers."""
        logger = get_component_logger("state_manager")
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, "gretah.scriptweaver.state_manager")
    
    def test_environment_variable_parsing(self):
        """Test environment variable configuration parsing."""
        os.environ['GRETAH_LOG_LEVEL'] = 'DEBUG'
        os.environ['GRETAH_LOG_CONSOLE'] = 'true'
        
        manager = GretahLoggingManager()
        
        self.assertEqual(manager.config['level'], logging.DEBUG)
        self.assertTrue(manager.config['console_output'])
    
    def test_backward_compatibility_environment_mapping(self):
        """Test that SCRIPTWEAVER_DEBUG maps to new configuration."""
        os.environ['SCRIPTWEAVER_DEBUG'] = 'true'
        
        manager = GretahLoggingManager()
        
        self.assertEqual(manager.config['level'], logging.DEBUG)
        self.assertTrue(manager.config['console_output'])
        self.assertTrue(manager.config['backward_compatibility'])


class TestEnhancedDebugFunctionality(unittest.TestCase):
    """Test enhanced debug functionality with structured logging."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Reset environment variables
        for var in ['SCRIPTWEAVER_DEBUG', 'GRETAH_LOG_LEVEL']:
            if var in os.environ:
                del os.environ[var]
    
    @patch('builtins.print')
    def test_enhanced_debug_call(self, mock_print):
        """Test enhanced debug calls with structured parameters."""
        os.environ['GRETAH_LOG_LEVEL'] = 'DEBUG'
        os.environ['GRETAH_LOG_CONSOLE'] = 'true'
        
        # Call debug with enhanced signature
        debug("Test message", stage="stage10", operation="test_operation", 
              context={'key': 'value'})
        
        # Verify console output is printed
        mock_print.assert_called_once_with("DEBUG: Test message")
    
    def test_mixed_debug_calls(self):
        """Test that legacy and enhanced debug calls can be mixed."""
        try:
            # Legacy call
            debug("Legacy message")
            
            # Enhanced call
            debug("Enhanced message", stage="stage10", operation="test")
            
            # Another legacy call
            debug("Another legacy message")
            
        except Exception as e:
            self.fail(f"Mixed debug calls failed: {e}")


class TestPerformanceAndErrorHandling(unittest.TestCase):
    """Test performance characteristics and error handling."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_debug_with_invalid_parameters(self):
        """Test that debug function handles invalid parameters gracefully."""
        try:
            # These should not raise exceptions
            debug("Test", stage=None, operation=None, context=None)
            debug("Test", stage="", operation="", context={})
            debug("Test", context={'complex': {'nested': 'data'}})
        except Exception as e:
            self.fail(f"Debug function failed with invalid parameters: {e}")
    
    def test_fallback_behavior_on_import_error(self):
        """Test fallback behavior when centralized logging is unavailable."""
        # Mock import error for centralized logging
        with patch('debug_utils.CENTRALIZED_LOGGING_AVAILABLE', False):
            try:
                debug("Test message")
                debug("Test message", stage="stage10")  # Should fall back to legacy
            except Exception as e:
                self.fail(f"Fallback behavior failed: {e}")
    
    def test_log_directory_creation(self):
        """Test that log directories are created properly."""
        manager = GretahLoggingManager()
        
        # Check that required directories exist
        expected_dirs = ["logs", "ai_logs", "ai_logs/requests", "ai_logs/errors", "ai_logs/metrics"]
        for dir_path in expected_dirs:
            self.assertTrue(Path(dir_path).exists(), f"Directory {dir_path} was not created")


class TestIntegrationWithExistingCode(unittest.TestCase):
    """Test integration with existing Stage 10 code patterns."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_stage10_debug_patterns(self):
        """Test typical Stage 10 debug patterns work unchanged."""
        # Simulate typical Stage 10 debug calls
        filename = "test_script.py"
        
        try:
            debug("Stage 10: Script Playground accessed")
            debug(f"Execute button clicked for {filename}")
            debug(f"Script execution completed and results stored: {filename}")
            debug(f"Copying script from script_path to local_script_path")
        except Exception as e:
            self.fail(f"Stage 10 debug patterns failed: {e}")


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
