# GRETAH Logging Standard

## Overview

The GRETAH Logging Standard provides a unified, structured logging approach for all GRETAH applications (ScriptWeaver, CaseForge, TestInsight). This standard ensures consistent logging patterns, centralized configuration, and optimal performance across the entire GRETAH suite.

## Key Features

- **Structured Logging**: All debug calls require stage, operation, and optional context parameters
- **Centralized Configuration**: Single source of truth for logging configuration
- **Hierarchical Organization**: Organized logger naming (gretah.application.stage)
- **Environment Control**: SCRIPTWEAVER_DEBUG and GRETAH_* environment variables
- **Performance Optimized**: Minimal overhead with conditional execution
- **Application Agnostic**: Reusable across all GRETAH applications

## Architecture

### Core Components

1. **GretahLoggingManager**: Singleton logging manager with centralized configuration
2. **debug() Function**: Standardized debug function with structured parameters
3. **Hierarchical Loggers**: Organized by application and stage/component
4. **Environment Configuration**: Flexible configuration via environment variables

### Logger Hierarchy

```
gretah.{application}                    # Root application logger
├── gretah.{application}.stage1         # Stage-specific loggers
├── gretah.{application}.stage2
├── ...
├── gretah.{application}.stage10
├── gretah.{application}.state_manager  # Component loggers
├── gretah.{application}.ai
└── gretah.{application}.ui
```

## Implementation Guide

### 1. Core Infrastructure Setup

#### Create `core/logging_config.py`

```python
"""
Centralized logging configuration for GRETAH applications.
"""

import os
import logging
import logging.handlers
from typing import Dict, Any
from pathlib import Path

class GretahLoggingManager:
    """Centralized logging manager for GRETAH applications."""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = GretahLoggingManager()
        return cls._instance
    
    def __init__(self):
        self.config = self._load_environment_config()
        self.loggers = {}
        self._setup_formatters()
        self._setup_root_logger()
        self._ensure_log_directories()
    
    def get_stage_logger(self, stage_name: str) -> logging.Logger:
        """Get or create a logger for a specific stage."""
        logger_name = f"gretah.{self.app_name}.{stage_name}"
        
        if logger_name not in self.loggers:
            logger = logging.getLogger(logger_name)
            self._configure_logger(logger, logger_name)
            self.loggers[logger_name] = logger
        
        return self.loggers[logger_name]
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        debug_enabled = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")
        
        return {
            'level': logging.DEBUG if debug_enabled else logging.INFO,
            'console_output': debug_enabled,
            'file_output': True,
            'ui_output': debug_enabled
        }

# Convenience functions
def get_stage_logger(stage_name: str) -> logging.Logger:
    return GretahLoggingManager.get_instance().get_stage_logger(stage_name)

def is_debug_enabled() -> bool:
    return GretahLoggingManager.get_instance().is_debug_enabled()
```

#### Create `debug_utils.py`

```python
"""
Standardized debug utilities for GRETAH applications.
"""

import os
import logging
import streamlit as st
from typing import Dict, Any, Optional

from core.logging_config import GretahLoggingManager, is_debug_enabled

DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message: str, stage: str, operation: str, context: Optional[Dict[str, Any]] = None):
    """
    Standardized debug function with structured logging.
    
    Args:
        message: Debug message (required)
        stage: Stage identifier (required - e.g., "stage1", "stage2")
        operation: Operation being performed (required - e.g., "file_upload", "validation")
        context: Additional context data (optional - dict with relevant metadata)
    
    Examples:
        debug("File upload initiated", stage="stage1", operation="file_upload")
        debug("Data processed", stage="stage1", operation="file_processing", 
              context={'rows': 100, 'filename': 'test.xlsx'})
    """
    try:
        manager = GretahLoggingManager.get_instance()
        stage_logger = manager.get_stage_logger(stage)

        if stage_logger.isEnabledFor(logging.DEBUG):
            extra_data = {'operation': operation}
            if context:
                extra_data['context'] = str(context)
            stage_logger.debug(message, extra=extra_data)

        if manager.should_output_to_console():
            print(f"DEBUG [{stage}:{operation}]: {message}")

    except Exception as e:
        print(f"DEBUG [FALLBACK]: {message} (Logging error: {e})")

    _handle_streamlit_debug(message, stage, operation)

def _handle_streamlit_debug(message: str, stage: str, operation: str):
    """Handle Streamlit debug UI integration."""
    try:
        if st._is_running and (DEBUG_MODE or is_debug_enabled()):
            if 'show_debug' not in st.session_state:
                st.session_state.show_debug = False
                st.session_state.debug_messages = []
            
            structured_message = f"[{stage}:{operation}] {message}"
            st.session_state.debug_messages.append(structured_message)
    except:
        pass
```

### 2. Stage Implementation

#### Update Stage Files

Each stage file should follow this pattern:

```python
"""
Stage X: Description
"""

import os
import logging
import streamlit as st

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stageX")

from debug_utils import debug

def stage_function(state):
    """Stage implementation with structured logging."""
    
    # Basic operation logging
    debug("Stage X initiated", 
          stage="stageX", 
          operation="stage_initialization")
    
    # Operation with context
    debug("Processing user input", 
          stage="stageX", 
          operation="input_processing",
          context={'input_type': 'file', 'size_mb': 2.5})
    
    # Error handling with context
    try:
        # ... stage logic ...
        debug("Stage X completed successfully", 
              stage="stageX", 
              operation="stage_completion",
              context={'duration_ms': 1500, 'success': True})
    except Exception as e:
        debug("Stage X failed", 
              stage="stageX", 
              operation="stage_error",
              context={'error': str(e), 'error_type': type(e).__name__})
        raise
```

### 3. Environment Configuration

#### Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `SCRIPTWEAVER_DEBUG` | Enable debug mode (legacy compatibility) | `false` | `true` |
| `GRETAH_LOG_LEVEL` | Logging level | `INFO` | `DEBUG` |
| `GRETAH_LOG_CONSOLE` | Console output | `false` | `true` |
| `GRETAH_LOG_FILE` | File output | `true` | `false` |
| `GRETAH_LOG_UI` | Streamlit UI output | `false` | `true` |
| `GRETAH_LOG_STAGE_FILTER` | Filter by stage | `` | `stage1` |

#### Configuration Examples

**Development Mode:**
```bash
export SCRIPTWEAVER_DEBUG=true
export GRETAH_LOG_LEVEL=DEBUG
export GRETAH_LOG_CONSOLE=true
```

**Production Mode:**
```bash
export SCRIPTWEAVER_DEBUG=false
export GRETAH_LOG_LEVEL=INFO
export GRETAH_LOG_CONSOLE=false
export GRETAH_LOG_FILE=true
```

**Stage-Specific Debugging:**
```bash
export GRETAH_LOG_STAGE_FILTER=stage4
export GRETAH_LOG_LEVEL=DEBUG
```

## Usage Patterns

### Standard Operations

```python
# File operations
debug("File upload started", stage="stage1", operation="file_upload")
debug("File processed", stage="stage1", operation="file_processing", 
      context={'filename': 'test.xlsx', 'rows': 100})

# AI operations  
debug("AI request initiated", stage="stage3", operation="ai_conversion")
debug("AI response received", stage="stage3", operation="ai_response",
      context={'model': 'gemini-2.0-flash', 'tokens': 1500})

# UI operations
debug("Button clicked", stage="stage5", operation="user_interaction",
      context={'button': 'submit', 'form_data': form_values})

# Error handling
debug("Validation failed", stage="stage2", operation="validation_error",
      context={'field': 'url', 'error': 'Invalid format'})
```

### Performance Monitoring

```python
import time

start_time = time.time()
# ... operation ...
duration = time.time() - start_time

debug("Operation completed", stage="stage6", operation="script_generation",
      context={'duration_ms': duration * 1000, 'success': True})
```

### Browser Session Tracking

```python
debug("Browser session started", stage="stage4", operation="browser_init",
      context={'url': target_url, 'headless': False})

debug("Element detected", stage="stage4", operation="element_detection",
      context={'selector': 'input[name="username"]', 'method': 'css'})
```

## Migration Guide

### From Legacy to Standardized

**Before (Legacy):**
```python
debug("File uploaded successfully")
debug(f"Processing {filename}")
```

**After (Standardized):**
```python
debug("File uploaded successfully", stage="stage1", operation="file_upload")
debug("Processing file", stage="stage1", operation="file_processing",
      context={'filename': filename})
```

### Automated Migration Script

```python
import re
import os

def migrate_debug_calls(file_path):
    """Migrate legacy debug calls to standardized format."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Extract stage name from file path
    stage_name = extract_stage_name(file_path)
    
    # Pattern to match legacy debug calls
    pattern = r'debug\("([^"]+)"\)'
    
    def replace_debug(match):
        message = match.group(1)
        operation = infer_operation(message)
        return f'debug("{message}", stage="{stage_name}", operation="{operation}")'
    
    updated_content = re.sub(pattern, replace_debug, content)
    
    with open(file_path, 'w') as f:
        f.write(updated_content)
```

## Performance Considerations

### Overhead Analysis

- **Structured Logging**: <1% performance impact
- **Conditional Execution**: Minimal overhead when debug disabled
- **Context Serialization**: Lazy evaluation for optimal performance

### Optimization Techniques

1. **Conditional Execution**: Check debug level before expensive operations
2. **Lazy Context**: Compute context only when needed
3. **Efficient Formatting**: Use f-strings and minimal string operations
4. **Structured Data**: JSON-serializable context for performance

## Troubleshooting

### Common Issues

1. **Missing Parameters**: Ensure all debug calls have stage and operation
2. **Import Errors**: Verify core/logging_config.py is properly imported
3. **Environment Variables**: Check SCRIPTWEAVER_DEBUG is set correctly
4. **File Permissions**: Ensure log directory is writable

### Diagnostic Commands

```python
# Check logging configuration
from core.logging_config import GretahLoggingManager
manager = GretahLoggingManager.get_instance()
print(f"Debug enabled: {manager.is_debug_enabled()}")
print(f"Console output: {manager.should_output_to_console()}")

# Test debug function
from debug_utils import debug
debug("Test message", stage="test", operation="diagnostic")
```

## Application-Specific Implementation

### GretahAI ScriptWeaver

- **Application Name**: `scriptweaver`
- **Stages**: `stage1` through `stage10`
- **Components**: `state_manager`, `ai`, `ui_components`

### GRETAH CaseForge

- **Application Name**: `caseforge`
- **Stages**: `upload`, `analysis`, `generation`
- **Components**: `csv_parser`, `ai_converter`, `export`

### GRETAH TestInsight

- **Application Name**: `testinsight`
- **Stages**: `import`, `execution`, `reporting`
- **Components**: `pytest_runner`, `result_analyzer`, `dashboard`

---

## License

© 2025 Cogniron All Rights Reserved.

This logging standard is part of the proprietary GRETAH AI suite. For licensing information, contact <EMAIL>.
