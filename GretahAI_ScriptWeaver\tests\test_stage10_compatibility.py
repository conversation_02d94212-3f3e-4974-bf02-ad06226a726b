"""
Stage 10 compatibility validation tests.

This test module validates that Stage 10's existing 67 debug statements
continue to work identically with the new logging infrastructure.

Tests verify:
1. All Stage 10 debug patterns work unchanged
2. SCRIPTWEAVER_DEBUG environment variable behavior preserved
3. Console output format identical to legacy implementation
4. No performance regression in Stage 10 operations
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules under test
from debug_utils import debug


class TestStage10DebugCompatibility(unittest.TestCase):
    """Test Stage 10 debug statement compatibility."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Reset environment variables
        if 'SCRIPTWEAVER_DEBUG' in os.environ:
            del os.environ['SCRIPTWEAVER_DEBUG']
    
    @patch('builtins.print')
    def test_stage10_debug_patterns_with_scriptweaver_debug_true(self, mock_print):
        """Test typical Stage 10 debug patterns with SCRIPTWEAVER_DEBUG=true."""
        os.environ['SCRIPTWEAVER_DEBUG'] = 'true'
        
        # Import fresh to pick up environment variable
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        # Test actual Stage 10 debug patterns
        filename = "test_login_template_20250127_103015.py"
        
        # Pattern 1: Simple stage access
        debug_utils.debug("Stage 10: Script Playground accessed")
        
        # Pattern 2: Button click with filename
        debug_utils.debug(f"Execute button clicked for {filename}")
        
        # Pattern 3: Operation completion with filename
        debug_utils.debug(f"Script execution completed and results stored: {filename}")
        
        # Pattern 4: File operations
        debug_utils.debug(f"Copying script from script_path to local_script_path")
        
        # Pattern 5: Template generation
        debug_utils.debug("Starting template-based script generation")
        
        # Pattern 6: AI integration
        debug_utils.debug("Calling Google AI for template-based script generation")
        
        # Pattern 7: Error handling
        debug_utils.debug(f"Template-based script generation error: Test error")
        
        # Pattern 8: Success handling
        debug_utils.debug("Handling successful template-based script generation")
        
        # Pattern 9: File storage
        debug_utils.debug(f"Template-based script saved with filename: {filename}")
        
        # Pattern 10: Navigation
        debug_utils.debug("User navigated to Stage 1 from Script Playground")
        
        # Verify all debug calls resulted in console output with "DEBUG: " prefix
        expected_calls = [
            "DEBUG: Stage 10: Script Playground accessed",
            f"DEBUG: Execute button clicked for {filename}",
            f"DEBUG: Script execution completed and results stored: {filename}",
            "DEBUG: Copying script from script_path to local_script_path",
            "DEBUG: Starting template-based script generation",
            "DEBUG: Calling Google AI for template-based script generation",
            "DEBUG: Template-based script generation error: Test error",
            "DEBUG: Handling successful template-based script generation",
            f"DEBUG: Template-based script saved with filename: {filename}",
            "DEBUG: User navigated to Stage 1 from Script Playground"
        ]
        
        # Verify each expected call was made
        actual_calls = [call.args[0] for call in mock_print.call_args_list]
        self.assertEqual(len(actual_calls), len(expected_calls))
        
        for expected, actual in zip(expected_calls, actual_calls):
            self.assertEqual(actual, expected)
    
    @patch('builtins.print')
    def test_stage10_debug_patterns_with_scriptweaver_debug_false(self, mock_print):
        """Test typical Stage 10 debug patterns with SCRIPTWEAVER_DEBUG=false."""
        os.environ['SCRIPTWEAVER_DEBUG'] = 'false'
        
        # Import fresh to pick up environment variable
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        # Test actual Stage 10 debug patterns
        filename = "test_login_template_20250127_103015.py"
        
        # All the same patterns as above
        debug_utils.debug("Stage 10: Script Playground accessed")
        debug_utils.debug(f"Execute button clicked for {filename}")
        debug_utils.debug(f"Script execution completed and results stored: {filename}")
        debug_utils.debug(f"Copying script from script_path to local_script_path")
        debug_utils.debug("Starting template-based script generation")
        debug_utils.debug("Calling Google AI for template-based script generation")
        debug_utils.debug(f"Template-based script generation error: Test error")
        debug_utils.debug("Handling successful template-based script generation")
        debug_utils.debug(f"Template-based script saved with filename: {filename}")
        debug_utils.debug("User navigated to Stage 1 from Script Playground")
        
        # Verify NO console output when DEBUG_MODE is false
        mock_print.assert_not_called()
    
    def test_stage10_complex_debug_patterns(self):
        """Test complex debug patterns used in Stage 10."""
        try:
            # Test with various data types and complex strings
            test_cases = [
                "Stage 10: Script Playground accessed",
                f"Execute button clicked for {'test_file.py'}",
                f"Script execution completed: {'test_file.py'}",
                "Enhanced context with gap analysis data",
                f"Raw generated script length: {1247} characters",
                f"Parsed script length: {1150} characters",
                "Incomplete generation data in session state, skipping results display",
                f"Displaying generation results for script: {'test_file.py'}",
                f"Successfully displayed generation results for: {'test_file.py'}",
                f"Error displaying script generation results: {'Test error'}"
            ]
            
            for test_case in test_cases:
                debug(test_case)
                
        except Exception as e:
            self.fail(f"Complex Stage 10 debug patterns failed: {e}")
    
    def test_stage10_debug_with_special_characters(self):
        """Test Stage 10 debug patterns with special characters and formatting."""
        try:
            # Test patterns with special characters used in Stage 10
            debug("🎮 Script Playground")
            debug("📄 Generated Script")
            debug("✅ Generated script cleared successfully!")
            debug("❌ **Generation Error**: Test error")
            debug("🤖 Generating script from template...")
            debug("🗑️ Clear Generated Script")
            debug("⚙️ Generate Scripts")
            debug("🔧 Optimize Scripts")
            debug("📁 Upload Test Cases")
            
        except Exception as e:
            self.fail(f"Stage 10 debug patterns with special characters failed: {e}")
    
    def test_stage10_performance_no_regression(self):
        """Test that Stage 10 debug performance has no significant regression."""
        import time
        
        # Test with SCRIPTWEAVER_DEBUG=false (should be very fast)
        os.environ['SCRIPTWEAVER_DEBUG'] = 'false'
        import importlib
        import debug_utils
        importlib.reload(debug_utils)
        
        start_time = time.time()
        
        # Simulate typical Stage 10 debug volume (67 statements)
        for i in range(67):
            debug_utils.debug(f"Debug statement {i}: Stage 10 operation")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete very quickly when debug is disabled
        self.assertLess(execution_time, 0.1, "Debug performance regression detected")
    
    def test_stage10_debug_with_none_values(self):
        """Test Stage 10 debug patterns with None values and edge cases."""
        try:
            # Test patterns that might occur with None values
            filename = None
            debug(f"Execute button clicked for {filename}")
            
            test_case_id = None
            debug(f"Target test case ID: {test_case_id}")
            
            error = None
            debug(f"Error occurred: {error}")
            
            # Test with empty strings
            debug("")
            debug("   ")
            
        except Exception as e:
            self.fail(f"Stage 10 debug patterns with None values failed: {e}")


class TestStage10EnhancedDebugCompatibility(unittest.TestCase):
    """Test Stage 10 compatibility with enhanced debug features."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_stage10_can_use_enhanced_debug_optionally(self):
        """Test that Stage 10 can optionally use enhanced debug features."""
        try:
            # Legacy calls (should work unchanged)
            debug("Stage 10: Script Playground accessed")
            
            # Enhanced calls (should work when parameters provided)
            debug("Script execution started", 
                  stage="stage10", 
                  operation="script_execution")
            
            debug("Template generation completed", 
                  stage="stage10", 
                  operation="template_generation",
                  context={'filename': 'test.py', 'success': True})
            
        except Exception as e:
            self.fail(f"Stage 10 enhanced debug compatibility failed: {e}")
    
    def test_stage10_mixed_debug_usage(self):
        """Test mixing legacy and enhanced debug calls in Stage 10 patterns."""
        try:
            # Simulate a typical Stage 10 workflow with mixed debug calls
            debug("Stage 10: Script Playground accessed")  # Legacy
            
            debug("Template selection initiated", 
                  stage="stage10", operation="template_selection")  # Enhanced
            
            debug(f"Execute button clicked for test_file.py")  # Legacy
            
            debug("Script generation completed", 
                  stage="stage10", 
                  operation="script_generation",
                  context={'duration': 2.3, 'success': True})  # Enhanced
            
            debug("User navigated to Stage 1 from Script Playground")  # Legacy
            
        except Exception as e:
            self.fail(f"Stage 10 mixed debug usage failed: {e}")


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
