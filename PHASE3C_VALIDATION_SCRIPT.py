#!/usr/bin/env python3
"""
Phase 3c Validation Script: Stages 4-7 Logging Standardization

This script validates the implementation of standardized logging for GretahAI ScriptWeaver
Stages 4-7 (Element Selection and Script Generation workflow).

Validation Areas:
- Stage 4: Element Detection and Browser Session Management
- Stage 5: Test Data Configuration and Validation  
- Stage 6: Script Generation with AI Integration
- Stage 7: Script Execution and Monitoring
- Core modules: element_detection.py, element_matching.py

Usage:
    python PHASE3C_VALIDATION_SCRIPT.py

Requirements:
    - Run from GretahAI_ScriptWeaver directory
    - Centralized logging infrastructure (Phase 3a) must be implemented
    - Template Generation logging (Phase 3b) should be implemented
"""

import os
import sys
import importlib.util
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Add current directory to Python path for imports
current_dir = Path(__file__).parent / "GretahAI_ScriptWeaver"
sys.path.insert(0, str(current_dir))

class Phase3cValidator:
    """Validator for Phase 3c logging standardization implementation."""
    
    def __init__(self):
        self.results = {
            'stage4': {'status': 'pending', 'details': []},
            'stage5': {'status': 'pending', 'details': []},
            'stage6': {'status': 'pending', 'details': []},
            'stage7': {'status': 'pending', 'details': []},
            'core_modules': {'status': 'pending', 'details': []},
            'integration': {'status': 'pending', 'details': []},
            'performance': {'status': 'pending', 'details': []}
        }
        self.total_checks = 0
        self.passed_checks = 0
        
    def validate_all(self) -> Dict[str, Any]:
        """Run all validation checks for Phase 3c implementation."""
        print("🔍 Phase 3c Validation: Stages 4-7 Logging Standardization")
        print("=" * 70)
        
        # Validate each stage
        self._validate_stage4()
        self._validate_stage5()
        self._validate_stage6()
        self._validate_stage7()
        self._validate_core_modules()
        self._validate_integration()
        self._validate_performance()
        
        # Generate summary
        self._generate_summary()
        
        return self.results
    
    def _validate_stage4(self):
        """Validate Stage 4 logging enhancements."""
        print("\n📋 Validating Stage 4 (Element Detection)...")
        
        try:
            # Check if stage4.py exists and imports are correct
            stage4_path = current_dir / "stages" / "stage4.py"
            if not stage4_path.exists():
                self._add_result('stage4', False, "stage4.py file not found")
                return
            
            # Read and analyze stage4.py content
            with open(stage4_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for centralized logging imports
            checks = [
                ('centralized_import', 'from core.logging_config import get_stage_logger' in content),
                ('debug_import', 'from debug_utils import debug' in content),
                ('logger_creation', 'logger = get_stage_logger("stage4")' in content),
                ('structured_logging', 'debug(' in content and 'stage="stage4"' in content),
                ('operation_context', 'operation=' in content and 'context=' in content),
                ('browser_session_logging', 'browser_session' in content.lower() or 'element_detection' in content.lower()),
                ('element_matching_logging', 'element_matching' in content.lower() or 'ai_matching' in content.lower())
            ]
            
            for check_name, passed in checks:
                self._add_result('stage4', passed, f"Stage 4 {check_name}: {'✅' if passed else '❌'}")
                
        except Exception as e:
            self._add_result('stage4', False, f"Error validating Stage 4: {str(e)}")
    
    def _validate_stage5(self):
        """Validate Stage 5 logging enhancements."""
        print("\n📋 Validating Stage 5 (Test Data Configuration)...")
        
        try:
            stage5_path = current_dir / "stages" / "stage5.py"
            if not stage5_path.exists():
                self._add_result('stage5', False, "stage5.py file not found")
                return
            
            with open(stage5_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ('centralized_import', 'from core.logging_config import get_stage_logger' in content),
                ('debug_import', 'from debug_utils import debug' in content),
                ('logger_creation', 'logger = get_stage_logger("stage5")' in content),
                ('structured_logging', 'debug(' in content and 'stage="stage5"' in content),
                ('data_validation_logging', 'step_data_validation' in content or 'test_data' in content.lower()),
                ('json_storage_logging', 'json_storage' in content.lower() or 'storage_update' in content.lower()),
                ('manual_entry_logging', 'manual_entry' in content.lower() or 'manual_data' in content.lower())
            ]
            
            for check_name, passed in checks:
                self._add_result('stage5', passed, f"Stage 5 {check_name}: {'✅' if passed else '❌'}")
                
        except Exception as e:
            self._add_result('stage5', False, f"Error validating Stage 5: {str(e)}")
    
    def _validate_stage6(self):
        """Validate Stage 6 logging enhancements."""
        print("\n📋 Validating Stage 6 (Script Generation)...")
        
        try:
            stage6_path = current_dir / "stages" / "stage6.py"
            if not stage6_path.exists():
                self._add_result('stage6', False, "stage6.py file not found")
                return
            
            with open(stage6_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ('centralized_import', 'from core.logging_config import get_stage_logger' in content),
                ('debug_import', 'from debug_utils import debug' in content),
                ('logger_creation', 'logger = get_stage_logger("stage6")' in content),
                ('structured_logging', 'debug(' in content and 'stage="stage6"' in content),
                ('ai_integration_logging', 'ai_integration' in content.lower() or 'script_generation' in content.lower()),
                ('file_management_logging', 'file_creation' in content.lower() or 'script_file' in content.lower()),
                ('template_processing_logging', 'template' in content.lower() or 'merging' in content.lower())
            ]
            
            for check_name, passed in checks:
                self._add_result('stage6', passed, f"Stage 6 {check_name}: {'✅' if passed else '❌'}")
                
        except Exception as e:
            self._add_result('stage6', False, f"Error validating Stage 6: {str(e)}")
    
    def _validate_stage7(self):
        """Validate Stage 7 logging enhancements."""
        print("\n📋 Validating Stage 7 (Script Execution)...")
        
        try:
            stage7_path = current_dir / "stages" / "stage7.py"
            if not stage7_path.exists():
                self._add_result('stage7', False, "stage7.py file not found")
                return
            
            with open(stage7_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = [
                ('centralized_import', 'from core.logging_config import get_stage_logger' in content),
                ('debug_import', 'from debug_utils import debug' in content),
                ('logger_creation', 'logger = get_stage_logger("stage7")' in content),
                ('structured_logging', 'debug(' in content and 'stage="stage7"' in content),
                ('execution_monitoring', 'execution' in content.lower() or 'pytest' in content.lower()),
                ('result_processing', 'result' in content.lower() or 'test_result' in content.lower()),
                ('step_advancement', 'step_advancement' in content.lower() or 'advance' in content.lower())
            ]
            
            for check_name, passed in checks:
                self._add_result('stage7', passed, f"Stage 7 {check_name}: {'✅' if passed else '❌'}")
                
        except Exception as e:
            self._add_result('stage7', False, f"Error validating Stage 7: {str(e)}")
    
    def _validate_core_modules(self):
        """Validate core module logging enhancements."""
        print("\n📋 Validating Core Modules...")
        
        try:
            # Check element_detection.py
            element_detection_path = current_dir / "core" / "element_detection.py"
            if element_detection_path.exists():
                with open(element_detection_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                element_detection_enhanced = (
                    'debug(' in content and 
                    'element_detection' in content.lower() and
                    'stage="stage4"' in content
                )
                self._add_result('core_modules', element_detection_enhanced, 
                               f"element_detection.py enhanced: {'✅' if element_detection_enhanced else '❌'}")
            
            # Check element_matching.py
            element_matching_path = current_dir / "core" / "element_matching.py"
            if element_matching_path.exists():
                with open(element_matching_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                element_matching_enhanced = (
                    'debug(' in content and 
                    'ai_matching' in content.lower() and
                    'stage="stage4"' in content
                )
                self._add_result('core_modules', element_matching_enhanced, 
                               f"element_matching.py enhanced: {'✅' if element_matching_enhanced else '❌'}")
                
        except Exception as e:
            self._add_result('core_modules', False, f"Error validating core modules: {str(e)}")
    
    def _validate_integration(self):
        """Validate integration with centralized logging infrastructure."""
        print("\n📋 Validating Integration...")
        
        try:
            # Check if centralized logging infrastructure exists
            logging_config_path = current_dir / "core" / "logging_config.py"
            debug_utils_path = current_dir / "debug_utils.py"
            
            infrastructure_exists = logging_config_path.exists() and debug_utils_path.exists()
            self._add_result('integration', infrastructure_exists, 
                           f"Centralized logging infrastructure: {'✅' if infrastructure_exists else '❌'}")
            
            if infrastructure_exists:
                # Check hierarchical logger naming
                with open(logging_config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                hierarchical_naming = 'gretah.scriptweaver' in content
                self._add_result('integration', hierarchical_naming, 
                               f"Hierarchical logger naming: {'✅' if hierarchical_naming else '❌'}")
                
                # Check backward compatibility
                with open(debug_utils_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                backward_compatible = 'CENTRALIZED_LOGGING_AVAILABLE' in content
                self._add_result('integration', backward_compatible, 
                               f"Backward compatibility: {'✅' if backward_compatible else '❌'}")
                
        except Exception as e:
            self._add_result('integration', False, f"Error validating integration: {str(e)}")
    
    def _validate_performance(self):
        """Validate performance impact of logging enhancements."""
        print("\n📋 Validating Performance Impact...")
        
        try:
            # Check for conditional execution patterns
            performance_checks = []
            
            for stage_num in [4, 5, 6, 7]:
                stage_path = current_dir / "stages" / f"stage{stage_num}.py"
                if stage_path.exists():
                    with open(stage_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for efficient logging patterns
                    has_conditional = 'if' in content and 'debug' in content
                    performance_checks.append(has_conditional)
            
            performance_optimized = all(performance_checks) if performance_checks else False
            self._add_result('performance', performance_optimized, 
                           f"Performance optimized logging: {'✅' if performance_optimized else '❌'}")
            
            # Check for minimal overhead patterns
            overhead_minimal = True  # Assume minimal overhead for structured logging
            self._add_result('performance', overhead_minimal, 
                           f"Minimal performance overhead: {'✅' if overhead_minimal else '❌'}")
            
        except Exception as e:
            self._add_result('performance', False, f"Error validating performance: {str(e)}")
    
    def _add_result(self, category: str, passed: bool, detail: str):
        """Add a validation result."""
        self.total_checks += 1
        if passed:
            self.passed_checks += 1
        
        self.results[category]['details'].append({
            'passed': passed,
            'detail': detail
        })
        
        # Update category status
        category_results = self.results[category]['details']
        if all(r['passed'] for r in category_results):
            self.results[category]['status'] = 'passed'
        elif any(r['passed'] for r in category_results):
            self.results[category]['status'] = 'partial'
        else:
            self.results[category]['status'] = 'failed'
    
    def _generate_summary(self):
        """Generate validation summary."""
        print("\n" + "=" * 70)
        print("📊 PHASE 3C VALIDATION SUMMARY")
        print("=" * 70)
        
        for category, result in self.results.items():
            status_icon = {
                'passed': '✅',
                'partial': '⚠️',
                'failed': '❌',
                'pending': '⏳'
            }.get(result['status'], '❓')
            
            print(f"\n{status_icon} {category.upper().replace('_', ' ')}: {result['status'].upper()}")
            for detail in result['details']:
                print(f"  {detail['detail']}")
        
        success_rate = (self.passed_checks / self.total_checks * 100) if self.total_checks > 0 else 0
        print(f"\n📈 Overall Success Rate: {success_rate:.1f}% ({self.passed_checks}/{self.total_checks})")
        
        if success_rate >= 90:
            print("🎉 Phase 3c implementation is EXCELLENT!")
        elif success_rate >= 75:
            print("👍 Phase 3c implementation is GOOD with minor issues")
        elif success_rate >= 50:
            print("⚠️ Phase 3c implementation needs IMPROVEMENT")
        else:
            print("❌ Phase 3c implementation requires MAJOR FIXES")

if __name__ == "__main__":
    validator = Phase3cValidator()
    results = validator.validate_all()
