"""
Enhanced debug utilities for GretahAI ScriptWeaver.

This module provides backward-compatible debug functionality while
integrating with the new centralized logging infrastructure.

Phase 3a Implementation - Enhanced Debug Utilities
Maintains 100% backward compatibility with existing debug() function calls
while adding optional structured logging capabilities.
"""

import os
import logging
import streamlit as st
import inspect
from typing import Dict, Any, Optional

# Import new centralized logging (with fallback for development/testing)
try:
    from core.logging_config import GretahLoggingManager, is_debug_enabled, should_output_to_console, should_output_to_ui
    CENTRALIZED_LOGGING_AVAILABLE = True
except ImportError:
    # Fallback for development/testing when centralized logging not available
    CENTRALIZED_LOGGING_AVAILABLE = False

# Maintain backward compatibility - configure logging exactly as before
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.debug_utils")

# Legacy DEBUG_MODE flag (preserved exactly for backward compatibility)
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message: str, stage: Optional[str] = None, operation: Optional[str] = None,
          context: Optional[Dict[str, Any]] = None):
    """
    Enhanced debug function with backward compatibility.

    This function maintains 100% backward compatibility with existing calls while
    adding optional structured logging capabilities when new parameters are provided.

    Args:
        message: Debug message (required - maintains existing signature)
        stage: Stage identifier (optional - new feature)
        operation: Operation being performed (optional - new feature)
        context: Additional context data (optional - new feature)

    Backward Compatibility:
        - All existing debug(message) calls work unchanged
        - SCRIPTWEAVER_DEBUG environment variable behavior preserved
        - Console output format identical to legacy implementation
        - Streamlit UI integration unchanged
    """

    # CRITICAL: Determine if this is a legacy call or enhanced call
    # Legacy calls: debug("message") - only message parameter provided
    # Enhanced calls: debug("message", stage="stage10", operation="test", context={...})
    is_enhanced_call = stage is not None or operation is not None or context is not None

    if CENTRALIZED_LOGGING_AVAILABLE and is_enhanced_call:
        # Use enhanced logging only when new parameters are provided
        _enhanced_debug(message, stage, operation, context)
    else:
        # Use legacy behavior for existing calls or when centralized logging unavailable
        _legacy_debug(message)

    # Maintain existing Streamlit integration (unchanged)
    _handle_streamlit_debug(message)

def _legacy_debug(message: str):
    """Exact replica of existing debug behavior for backward compatibility."""
    # Always log to console (exactly as before)
    logger.debug(message)

    # Also print to console for immediate visibility during development (exactly as before)
    if DEBUG_MODE:
        print(f"DEBUG: {message}")

def _enhanced_debug(message: str, stage: Optional[str], operation: Optional[str],
                   context: Optional[Dict[str, Any]]):
    """Enhanced debug with structured logging capabilities."""
    try:
        manager = GretahLoggingManager.get_instance()

        # Determine stage if not provided
        if not stage:
            stage = _get_calling_stage()

        stage_logger = manager.get_stage_logger(stage)

        # Log with structured data if debug level enabled
        if stage_logger.isEnabledFor(logging.DEBUG):
            extra_data = {}
            if operation:
                extra_data['operation'] = operation
            if context:
                extra_data['context'] = str(context)

            stage_logger.debug(message, extra=extra_data)

        # Maintain console output behavior (preserve SCRIPTWEAVER_DEBUG compatibility)
        if manager.should_output_to_console():
            print(f"DEBUG: {message}")

    except Exception:
        # Fallback to legacy behavior on any error to ensure reliability
        _legacy_debug(message)

def _get_calling_stage() -> str:
    """Determine the calling stage from the call stack."""
    try:
        frame = inspect.currentframe()
        while frame:
            filename = frame.f_code.co_filename
            if 'stage' in filename and filename.endswith('.py'):
                # Extract stage name from filename (e.g., stage10.py -> stage10)
                stage_name = os.path.basename(filename).replace('.py', '')
                return stage_name
            frame = frame.f_back
        return "unknown"
    except:
        return "unknown"

def _handle_streamlit_debug(message: str):
    """Maintain existing Streamlit debug behavior (unchanged from legacy implementation)."""
    try:
        # Check if we're in a Streamlit context with an active session state
        if st._is_running:
            # Determine if debug is enabled (legacy or enhanced)
            debug_enabled = DEBUG_MODE
            if CENTRALIZED_LOGGING_AVAILABLE:
                debug_enabled = debug_enabled or is_debug_enabled()

            if debug_enabled:
                # Create a debug section in the sidebar if it doesn't exist (exactly as before)
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []

                # Store the message (exactly as before)
                st.session_state.debug_messages.append(message)
    except:
        # If any error occurs, just continue without UI display (exactly as before)
        pass

# Maintain existing function for compatibility
def get_debug_mode() -> bool:
    """Get current debug mode status (backward compatible)."""
    if CENTRALIZED_LOGGING_AVAILABLE:
        # Enhanced mode: check both legacy and new debug settings
        manager = GretahLoggingManager.get_instance()
        return DEBUG_MODE or manager.is_debug_enabled()
    return DEBUG_MODE
