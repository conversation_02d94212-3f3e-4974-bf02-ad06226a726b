"""
Standardized debug utilities for GretahAI ScriptWeaver.

This module provides structured logging functionality using the centralized
logging infrastructure. All debug calls must use the enhanced structured format.

GRETAH Logging Standard Implementation
"""

import os
import logging
import streamlit as st
from typing import Dict, Any, Optional

# Import centralized logging infrastructure
from core.logging_config import GretahLoggingManager, is_debug_enabled, should_output_to_console, should_output_to_ui

# Environment-based debug control
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message: str, stage: str, operation: str, context: Optional[Dict[str, Any]] = None):
    """
    Standardized debug function with structured logging.

    All debug calls must provide stage and operation parameters for proper
    categorization and filtering in the centralized logging system.

    Args:
        message: Debug message (required)
        stage: Stage identifier (required - e.g., "stage1", "stage2", etc.)
        operation: Operation being performed (required - e.g., "file_upload", "validation")
        context: Additional context data (optional - dict with relevant metadata)

    Examples:
        debug("File upload initiated", stage="stage1", operation="file_upload")
        debug("Data processed successfully", stage="stage1", operation="file_processing",
              context={'rows': 100, 'filename': 'test.xlsx'})
        debug("Element detection started", stage="stage4", operation="element_detection",
              context={'step_count': 5, 'url': 'https://example.com'})
    """
    try:
        manager = GretahLoggingManager.get_instance()
        stage_logger = manager.get_stage_logger(stage)

        # Log with structured data if debug level enabled
        if stage_logger.isEnabledFor(logging.DEBUG):
            extra_data = {'operation': operation}
            if context:
                extra_data['context'] = str(context)

            stage_logger.debug(message, extra=extra_data)

        # Maintain console output behavior (preserve SCRIPTWEAVER_DEBUG compatibility)
        if manager.should_output_to_console():
            print(f"DEBUG [{stage}:{operation}]: {message}")

    except Exception as e:
        # Fallback to basic logging on any error to ensure reliability
        print(f"DEBUG [FALLBACK]: {message} (Logging error: {e})")

    # Maintain existing Streamlit integration
    _handle_streamlit_debug(message, stage, operation)

def _handle_streamlit_debug(message: str, stage: str, operation: str):
    """Handle Streamlit debug UI integration with structured logging."""
    try:
        # Check if we're in a Streamlit context with an active session state
        if st._is_running:
            # Determine if debug is enabled
            debug_enabled = DEBUG_MODE or is_debug_enabled()

            if debug_enabled:
                # Create a debug section in the sidebar if it doesn't exist
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []

                # Store the structured message
                structured_message = f"[{stage}:{operation}] {message}"
                st.session_state.debug_messages.append(structured_message)
    except:
        # If any error occurs, just continue without UI display
        pass

def get_debug_mode() -> bool:
    """Get current debug mode status."""
    try:
        manager = GretahLoggingManager.get_instance()
        return DEBUG_MODE or manager.is_debug_enabled()
    except:
        return DEBUG_MODE
