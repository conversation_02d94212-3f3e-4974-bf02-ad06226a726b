#!/usr/bin/env python3
"""
GRETAH Logging Standard Validation Script

This script validates the implementation of the GRETAH Logging Standard
across the GretahAI ScriptWeaver application.

Features:
- Validates core logging infrastructure
- Checks debug function standardization
- Verifies environment variable support
- Tests hierarchical logger functionality
- Provides comprehensive diagnostic output

Usage:
    python validate_logging_standard.py
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, List, Tuple

class LoggingStandardValidator:
    """Validates GRETAH Logging Standard implementation."""
    
    def __init__(self):
        self.results = {
            'infrastructure': {'passed': 0, 'failed': 0, 'details': []},
            'debug_function': {'passed': 0, 'failed': 0, 'details': []},
            'environment': {'passed': 0, 'failed': 0, 'details': []},
            'hierarchical': {'passed': 0, 'failed': 0, 'details': []},
            'integration': {'passed': 0, 'failed': 0, 'details': []}
        }
    
    def validate_all(self) -> Dict:
        """Run all validation tests."""
        print("🔍 GRETAH Logging Standard Validation")
        print("=" * 50)
        
        self._validate_infrastructure()
        self._validate_debug_function()
        self._validate_environment_support()
        self._validate_hierarchical_loggers()
        self._validate_integration()
        
        return self.results
    
    def _validate_infrastructure(self):
        """Validate core logging infrastructure."""
        print("\n📋 Validating Core Infrastructure...")
        
        # Test 1: Check if core/logging_config.py exists
        try:
            from core.logging_config import GretahLoggingManager
            self._record_pass('infrastructure', "✅ Core logging module imports successfully")
        except ImportError as e:
            self._record_fail('infrastructure', f"❌ Core logging module import failed: {e}")
            return
        
        # Test 2: Check GretahLoggingManager singleton
        try:
            manager1 = GretahLoggingManager.get_instance()
            manager2 = GretahLoggingManager.get_instance()
            if manager1 is manager2:
                self._record_pass('infrastructure', "✅ Singleton pattern working correctly")
            else:
                self._record_fail('infrastructure', "❌ Singleton pattern failed")
        except Exception as e:
            self._record_fail('infrastructure', f"❌ Singleton test failed: {e}")
        
        # Test 3: Check debug_utils.py
        try:
            from debug_utils import debug
            self._record_pass('infrastructure', "✅ Debug utilities module imports successfully")
        except ImportError as e:
            self._record_fail('infrastructure', f"❌ Debug utilities import failed: {e}")
    
    def _validate_debug_function(self):
        """Validate standardized debug function."""
        print("\n🔧 Validating Debug Function...")
        
        try:
            from debug_utils import debug
            
            # Test 1: Check function signature
            import inspect
            sig = inspect.signature(debug)
            params = list(sig.parameters.keys())
            
            if 'message' in params and 'stage' in params and 'operation' in params:
                self._record_pass('debug_function', "✅ Debug function has required parameters")
            else:
                self._record_fail('debug_function', f"❌ Debug function missing required parameters: {params}")
            
            # Test 2: Test function call (should not raise exception)
            try:
                debug("Test message", stage="test", operation="validation")
                self._record_pass('debug_function', "✅ Debug function executes without errors")
            except Exception as e:
                self._record_fail('debug_function', f"❌ Debug function execution failed: {e}")
                
        except Exception as e:
            self._record_fail('debug_function', f"❌ Debug function validation failed: {e}")
    
    def _validate_environment_support(self):
        """Validate environment variable support."""
        print("\n🌍 Validating Environment Support...")
        
        try:
            from core.logging_config import GretahLoggingManager
            
            # Test 1: Check SCRIPTWEAVER_DEBUG support
            original_value = os.environ.get("SCRIPTWEAVER_DEBUG")
            
            # Test with debug enabled
            os.environ["SCRIPTWEAVER_DEBUG"] = "true"
            manager = GretahLoggingManager()
            if manager.is_debug_enabled():
                self._record_pass('environment', "✅ SCRIPTWEAVER_DEBUG=true enables debug mode")
            else:
                self._record_fail('environment', "❌ SCRIPTWEAVER_DEBUG=true failed to enable debug")
            
            # Test with debug disabled
            os.environ["SCRIPTWEAVER_DEBUG"] = "false"
            manager = GretahLoggingManager()
            if not manager.is_debug_enabled():
                self._record_pass('environment', "✅ SCRIPTWEAVER_DEBUG=false disables debug mode")
            else:
                self._record_fail('environment', "❌ SCRIPTWEAVER_DEBUG=false failed to disable debug")
            
            # Restore original value
            if original_value is not None:
                os.environ["SCRIPTWEAVER_DEBUG"] = original_value
            elif "SCRIPTWEAVER_DEBUG" in os.environ:
                del os.environ["SCRIPTWEAVER_DEBUG"]
                
        except Exception as e:
            self._record_fail('environment', f"❌ Environment validation failed: {e}")
    
    def _validate_hierarchical_loggers(self):
        """Validate hierarchical logger functionality."""
        print("\n🌳 Validating Hierarchical Loggers...")
        
        try:
            from core.logging_config import GretahLoggingManager
            
            manager = GretahLoggingManager.get_instance()
            
            # Test 1: Create stage loggers
            stage_loggers = []
            for i in range(1, 4):  # Test first 3 stages
                logger = manager.get_stage_logger(f"stage{i}")
                stage_loggers.append(logger)
                
                if logger.name.endswith(f"stage{i}"):
                    self._record_pass('hierarchical', f"✅ Stage{i} logger created with correct name")
                else:
                    self._record_fail('hierarchical', f"❌ Stage{i} logger has incorrect name: {logger.name}")
            
            # Test 2: Check logger caching
            logger1 = manager.get_stage_logger("stage1")
            logger2 = manager.get_stage_logger("stage1")
            if logger1 is logger2:
                self._record_pass('hierarchical', "✅ Logger caching working correctly")
            else:
                self._record_fail('hierarchical', "❌ Logger caching failed")
                
        except Exception as e:
            self._record_fail('hierarchical', f"❌ Hierarchical logger validation failed: {e}")
    
    def _validate_integration(self):
        """Validate integration with existing systems."""
        print("\n🔗 Validating System Integration...")
        
        # Test 1: Check if stage files can import logging
        stage_files = list(Path("stages").glob("stage*.py"))
        if stage_files:
            self._record_pass('integration', f"✅ Found {len(stage_files)} stage files for testing")
            
            # Test a few stage files for import capability
            for stage_file in stage_files[:3]:  # Test first 3 stages
                try:
                    stage_name = stage_file.stem
                    # This is a basic import test - in real usage, we'd need more sophisticated testing
                    self._record_pass('integration', f"✅ {stage_name} file accessible for integration")
                except Exception as e:
                    self._record_fail('integration', f"❌ {stage_name} integration test failed: {e}")
        else:
            self._record_fail('integration', "❌ No stage files found for integration testing")
        
        # Test 2: Check documentation exists
        if Path("GRETAH_LOGGING_STANDARD.md").exists():
            self._record_pass('integration', "✅ GRETAH Logging Standard documentation exists")
        else:
            self._record_fail('integration', "❌ GRETAH Logging Standard documentation missing")
    
    def _record_pass(self, category: str, message: str):
        """Record a passing test."""
        self.results[category]['passed'] += 1
        self.results[category]['details'].append(message)
        print(f"  {message}")
    
    def _record_fail(self, category: str, message: str):
        """Record a failing test."""
        self.results[category]['failed'] += 1
        self.results[category]['details'].append(message)
        print(f"  {message}")
    
    def generate_report(self) -> str:
        """Generate comprehensive validation report."""
        report = []
        report.append("=" * 60)
        report.append("GRETAH LOGGING STANDARD VALIDATION REPORT")
        report.append("=" * 60)
        
        total_passed = sum(cat['passed'] for cat in self.results.values())
        total_failed = sum(cat['failed'] for cat in self.results.values())
        total_tests = total_passed + total_failed
        
        report.append(f"Overall Results: {total_passed}/{total_tests} tests passed")
        report.append(f"Success Rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "No tests run")
        report.append("")
        
        for category, results in self.results.items():
            category_total = results['passed'] + results['failed']
            if category_total > 0:
                report.append(f"{category.upper()} VALIDATION:")
                report.append(f"  Passed: {results['passed']}/{category_total}")
                report.append(f"  Failed: {results['failed']}/{category_total}")
                report.append("")
                
                for detail in results['details']:
                    report.append(f"  {detail}")
                report.append("")
        
        # Overall assessment
        if total_failed == 0:
            report.append("🎉 VALIDATION SUCCESSFUL!")
            report.append("The GRETAH Logging Standard is properly implemented.")
        else:
            report.append("⚠️  VALIDATION ISSUES DETECTED")
            report.append(f"{total_failed} test(s) failed. Review the details above.")
        
        report.append("")
        report.append("© 2025 Cogniron All Rights Reserved")
        
        return "\n".join(report)

def main():
    """Main validation function."""
    print("Starting GRETAH Logging Standard validation...")
    print("This will test the implementation of the standardized logging system")
    print()
    
    # Run validation
    validator = LoggingStandardValidator()
    results = validator.validate_all()
    
    # Generate and display report
    report = validator.generate_report()
    print("\n" + report)
    
    # Save report to file
    with open("logging_standard_validation_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\nDetailed report saved to: logging_standard_validation_report.txt")
    
    # Return appropriate exit code
    total_failed = sum(cat['failed'] for cat in results.values())
    return 0 if total_failed == 0 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
