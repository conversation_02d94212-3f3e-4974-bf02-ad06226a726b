"""
Stage 5: Test Data Configuration for GretahAI ScriptWeaver

This module handles the test data configuration phase of the application workflow.
It provides functionality for:
- Manual test data entry for specific test steps
- Test data categorization and display
- Integration with JSON storage for data persistence

The stage supports manual data entry through user input fields with common
test data templates and suggestions. Test data is properly categorized and
stored in the state manager for use in script generation.

Functions:
    stage5_test_data(state): Main Stage 5 function for test data configuration
    _display_test_data(state, step_test_data): Helper function for displaying test data
    _get_test_data_suggestions(step_data): Helper function for test data suggestions

Phase 3c Enhancement: Standardized logging with centralized infrastructure
"""

import logging
import streamlit as st
from state_manager import StateStage

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
from debug_utils import debug
logger = get_stage_logger("stage5")


def stage5_test_data(state):
    """
    Phase 5: Configure Test Data for Selected Test Case Step.

    This function handles the test data configuration phase, allowing users to
    manually enter test data for the selected test step.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 5: Configure Test Data</h2>", unsafe_allow_html=True)

    # Enhanced logging for Stage 5 initialization
    debug("Stage 5: Test Data Configuration Started",
          stage="stage5",
          operation="stage_initialization")

    # Log current step data for debugging
    selected_step = getattr(state, 'selected_step', None)
    selected_step_table_entry = getattr(state, 'selected_step_table_entry', None)

    debug("Stage 5: Current step data analysis",
          stage="stage5",
          operation="step_data_validation",
          context={
              'has_selected_step': bool(selected_step),
              'has_step_table_entry': bool(selected_step_table_entry),
              'step_no': selected_step.get('Step No') if selected_step else None,
              'test_steps': selected_step.get('Test Steps') if selected_step else None,
              'table_step_no': selected_step_table_entry.get('step_no') if selected_step_table_entry else None,
              'table_action': selected_step_table_entry.get('action') if selected_step_table_entry else None
          })

    debug("Stage 5: Initial validation completed",
          stage="stage5",
          operation="initial_validation")

    # Check if we have the required data from previous stages
    if not state.selected_test_case:
        st.error("❌ No test case selected. Please complete Phase 1 first.")
        return

    if not selected_step:
        st.error("❌ No test step selected. Please complete Phase 4 first.")
        return

    if not selected_step_table_entry:
        st.warning("⚠️ No step table entry found. Please complete Phase 3 first.")
        return

    # Display step information
    step_no = selected_step.get('Step No', 'Unknown')
    step_action = selected_step.get('Test Steps', '')

    with st.expander("📋 Step Information", expanded=True):
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**Step Number:** {step_no}")
            st.markdown(f"**Action:** {step_action}")
        with col2:
            if selected_step_table_entry:
                st.markdown(f"**Step Type:** {selected_step_table_entry.get('step_type', 'N/A')}")
                st.markdown(f"**Locator:** {selected_step_table_entry.get('locator', 'N/A')}")

    # Test data configuration section
    with st.expander("✏️ Manual Test Data Entry", expanded=True):
        st.markdown("Enter test data that will be used during script execution for this step.")

        # Get test data suggestions based on step action
        suggestions = _get_test_data_suggestions(selected_step_table_entry or selected_step)

        if suggestions:
            st.markdown("**💡 Suggested data types for this step:**")
            for suggestion in suggestions:
                st.markdown(f"- {suggestion}")
            st.markdown("---")

        # Manual test data input form
        _render_manual_test_data_form(state, selected_step, selected_step_table_entry, suggestions)

    # Skip option
    with st.expander("⏭️ Skip Test Data", expanded=False):
        st.markdown("If this step doesn't require test data, you can skip this phase.")

        if st.button("Skip Test Data Configuration", key="skip_test_data_btn",
                    help="Skip test data configuration and proceed to Phase 6"):
            # Set skip flag and ensure test_data exists
            state.test_data_skipped = True
            if not hasattr(state, 'test_data') or not isinstance(state.test_data, dict):
                state.test_data = {}
            state.step_ready_for_script = True

            # Update JSON storage to indicate test data was skipped
            _update_json_storage_skip(state, step_no)

            # Add indicator to sidebar
            with st.sidebar:
                st.success("✓ Test data skipped")
                st.info("Ready for Phase 6")

            st.success("✓ Test data configuration skipped")

            # Automatically advance to Stage 6
            if state.current_stage == StateStage.STAGE5_DATA:
                state.advance_to(StateStage.STAGE6_GENERATE, "Test data skipped - advancing to Stage 6")
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Test data skipped. Proceeding to Stage 6."
                st.rerun()
                return
    # Display current test data if any exists
    if hasattr(state, 'test_data') and state.test_data:
        with st.expander("📊 Current Test Data", expanded=False):
            _display_test_data(state, state.test_data)





def _display_test_data(state, test_data):
    """
    Display test data in a simple, organized format.

    Args:
        state (StateManager): The application state manager instance
        test_data (dict): Dictionary containing the test data
    """
    if test_data:
        st.markdown("**Current Test Data:**")

        # Group manual and other data
        manual_data = {}
        other_data = {}

        for key, value in test_data.items():
            if 'manual_input_for_step_' in key or 'step_' in key:
                manual_data[key] = value
            else:
                other_data[key] = value

        # Display manual data
        if manual_data:
            st.markdown("**Manual Entries:**")
            for key, value in manual_data.items():
                if 'manual_input_for_step_' in key:
                    step_num = key.replace('manual_input_for_step_', '')
                    st.markdown(f"- **Step {step_num}:** `{value}`")
                elif 'step_' in key:
                    step_num = key.replace('step_', '')
                    st.markdown(f"- **Step {step_num}:** `{value}`")
                else:
                    st.markdown(f"- **{key}:** `{value}`")

        # Display other data
        if other_data:
            st.markdown("**Other Parameters:**")
            for key, value in other_data.items():
                st.markdown(f"- **{key}:** `{value}`")
    else:
        st.info("No test data configured")


def _get_test_data_suggestions(step_data):
    """
    Get test data suggestions based on step action.

    Args:
        step_data (dict): Step data containing action information

    Returns:
        list: List of suggested test data types
    """
    if not step_data:
        return []

    action = step_data.get('action', '') or step_data.get('Test Steps', '')
    if not action:
        return []

    action_lower = action.lower()
    suggestions = []

    # Common patterns for different types of actions
    if any(keyword in action_lower for keyword in ['login', 'sign in', 'authenticate']):
        suggestions = ['Username/Email', 'Password']
    elif any(keyword in action_lower for keyword in ['search', 'find', 'query']):
        suggestions = ['Search term', 'Keywords']
    elif any(keyword in action_lower for keyword in ['email', 'mail']):
        suggestions = ['Email address']
    elif any(keyword in action_lower for keyword in ['name', 'first name', 'last name']):
        suggestions = ['First name', 'Last name', 'Full name']
    elif any(keyword in action_lower for keyword in ['phone', 'mobile', 'contact']):
        suggestions = ['Phone number', 'Mobile number']
    elif any(keyword in action_lower for keyword in ['address', 'street', 'city']):
        suggestions = ['Street address', 'City', 'State', 'ZIP code']
    elif any(keyword in action_lower for keyword in ['select', 'choose', 'pick']):
        suggestions = ['Selection value', 'Option text']
    elif any(keyword in action_lower for keyword in ['enter', 'input', 'type', 'fill']):
        suggestions = ['Text input', 'Form data']
    elif any(keyword in action_lower for keyword in ['click', 'press', 'tap']):
        suggestions = ['No data needed (click action)']
    else:
        suggestions = ['Custom test data']

    return suggestions


def _render_manual_test_data_form(state, selected_step, selected_step_table_entry, suggestions):
    """
    Render the manual test data input form.

    Args:
        state (StateManager): The application state manager instance
        selected_step (dict): Selected step data
        selected_step_table_entry (dict): Selected step table entry
        suggestions (list): List of suggested data types
    """
    step_no = selected_step.get('Step No', 'Unknown')

    # Initialize test data if not exists
    if not hasattr(state, 'test_data') or not isinstance(state.test_data, dict):
        state.test_data = {}

    # Get existing test data for this step
    step_key = f"manual_input_for_step_{step_no}"
    existing_value = state.test_data.get(step_key, "")

    # Create input field
    test_data_value = st.text_input(
        f"Test Data for Step {step_no}:",
        value=existing_value,
        key=f"manual_test_data_{step_no}",
        help="Enter the test data that will be used during script execution"
    )

    # Save button
    col1, col2 = st.columns([1, 3])
    with col1:
        if st.button("💾 Save Data", key=f"save_test_data_{step_no}"):
            if test_data_value.strip():
                # Save to state
                state.test_data[step_key] = test_data_value.strip()

                # Add website_url if available for first step navigation
                if hasattr(state, 'website_url') and state.website_url:
                    state.test_data['website_url'] = state.website_url

                state.step_ready_for_script = True

                # Update JSON storage
                _update_json_storage_manual(state, step_no, step_key, test_data_value.strip())

                st.success(f"✅ Test data saved for Step {step_no}")

                # Auto-advance to Stage 6
                if state.current_stage == StateStage.STAGE5_DATA:
                    state.advance_to(StateStage.STAGE6_GENERATE, "Manual test data saved - advancing to Stage 6")
                    st.session_state['state'] = state
                    st.session_state['stage_progression_message'] = "✅ Test data saved. Proceeding to Stage 6."
                    st.rerun()
                    return
            else:
                st.warning("Please enter some test data before saving")

    with col2:
        if existing_value:
            if st.button("🗑️ Clear Data", key=f"clear_test_data_{step_no}"):
                if step_key in state.test_data:
                    del state.test_data[step_key]
                st.success("Test data cleared")
                st.rerun()


def _update_json_storage_manual(state, step_no, param_name, value):
    """
    Update JSON storage with manual test data.

    Args:
        state (StateManager): The application state manager instance
        step_no (str): Step number
        param_name (str): Parameter name
        value (str): Test data value
    """
    try:
        from datetime import datetime
        current_step_data = state.get_effective_step_table()

        # Find and update the current step with manual test data
        for step in current_step_data:
            if str(step.get('step_no')) == str(step_no):
                if '_test_data' not in step:
                    step['_test_data'] = {}
                step['_test_data'][param_name] = value
                step['_test_data_generated'] = True
                step['_test_data_timestamp'] = datetime.now().isoformat()
                step['_test_data_method'] = 'manual_entry'
                break

        # Save updated step data to JSON
        state.update_step_data_in_json(current_step_data, f"manual_test_data_step_{step_no}", {
            'step_no': step_no,
            'manual_data_key': param_name,
            'manual_data_value': value,
            'generation_method': 'manual_entry'
        })
        debug("Updated JSON storage with manual test data",
              stage="stage5",
              operation="json_storage_update",
              context={
                  'step_no': step_no,
                  'param_name': param_name,
                  'value_length': len(str(value)),
                  'method': 'manual_entry'
              })
    except Exception as e:
        debug("Failed to update JSON storage with manual test data",
              stage="stage5",
              operation="json_storage_update",
              context={
                  'step_no': step_no,
                  'error': str(e),
                  'method': 'manual_entry'
              })


def _update_json_storage_skip(state, step_no):
    """
    Update JSON storage to indicate test data was skipped.

    Args:
        state (StateManager): The application state manager instance
        step_no (str): Step number
    """
    try:
        from datetime import datetime
        current_step_data = state.get_effective_step_table()

        # Find and update the current step to indicate test data was skipped
        for step in current_step_data:
            if str(step.get('step_no')) == str(step_no):
                step['_test_data_skipped'] = True
                step['_test_data_timestamp'] = datetime.now().isoformat()
                step['_test_data_method'] = 'skipped'
                break

        # Save updated step data to JSON
        state.update_step_data_in_json(current_step_data, f"test_data_skipped_step_{step_no}", {
            'step_no': step_no,
            'generation_method': 'skipped'
        })
        debug("Updated JSON storage - test data skipped",
              stage="stage5",
              operation="json_storage_update",
              context={
                  'step_no': step_no,
                  'method': 'skipped'
              })
    except Exception as e:
        debug("Failed to update JSON storage for skipped test data",
              stage="stage5",
              operation="json_storage_update",
              context={
                  'step_no': step_no,
                  'error': str(e),
                  'method': 'skipped'
              })
