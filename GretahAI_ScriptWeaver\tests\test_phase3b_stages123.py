"""
Unit tests for Phase 3b implementation - Stages 1-3 logging standardization.

Tests verify:
1. Enhanced logging functionality in Stages 1-3
2. Backward compatibility with existing functionality
3. Structured logging with centralized infrastructure
4. Performance characteristics and error handling
5. Integration with GretahLoggingManager

Phase 3b Implementation - Template Generation Workflow Testing
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules under test
from debug_utils import debug
from core.logging_config import GretahLoggingManager, get_stage_logger


class TestStage1LoggingEnhancements(unittest.TestCase):
    """Test Stage 1 logging enhancements."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        import logging
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_stage1_logger_creation(self):
        """Test that Stage 1 uses centralized logger."""
        logger = get_stage_logger("stage1")
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "gretah.scriptweaver.stage1")
    
    def test_stage1_validate_uploaded_file_logging(self):
        """Test enhanced logging in validate_uploaded_file function."""
        # Import the function to test
        from stages.stage1 import validate_uploaded_file
        
        # Create mock uploaded file
        mock_file = MagicMock()
        mock_file.name = "test_file.xlsx"
        
        # Test with valid file
        valid_content = b'PK' + b'x' * 1000  # Valid Excel signature + content
        
        with patch('stages.stage1.debug') as mock_debug:
            errors = validate_uploaded_file(mock_file, valid_content)
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            
            # Check that structured logging parameters were used
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage1"' in str(call) for call in calls))
            self.assertTrue(any('operation=' in str(call) for call in calls))
    
    def test_stage1_safe_get_test_case_count_logging(self):
        """Test enhanced logging in safe_get_test_case_count function."""
        from stages.stage1 import safe_get_test_case_count
        
        with patch('stages.stage1.debug') as mock_debug:
            # Test with valid list
            result = safe_get_test_case_count([{'id': 1}, {'id': 2}])
            self.assertEqual(result, 2)
            
            # Test with invalid data
            result = safe_get_test_case_count("not a list")
            self.assertEqual(result, 0)
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage1"' in str(call) for call in calls))
    
    def test_stage1_parse_excel_cached_logging(self):
        """Test enhanced logging in parse_excel_cached function."""
        from stages.stage1 import parse_excel_cached
        
        # Mock the parse_excel function
        with patch('stages.stage1.parse_excel') as mock_parse_excel, \
             patch('stages.stage1.debug') as mock_debug:
            
            mock_parse_excel.return_value = [{'Test Case ID': 'TC001'}]
            
            # Test with valid Excel content
            test_content = b'PK' + b'x' * 1000
            
            result = parse_excel_cached(test_content)
            
            # Verify function worked
            self.assertIsNotNone(result)
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage1"' in str(call) for call in calls))
            self.assertTrue(any('operation=' in str(call) for call in calls))


class TestStage2LoggingEnhancements(unittest.TestCase):
    """Test Stage 2 logging enhancements."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        import logging
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_stage2_logger_creation(self):
        """Test that Stage 2 uses centralized logger."""
        logger = get_stage_logger("stage2")
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "gretah.scriptweaver.stage2")
    
    def test_stage2_load_google_api_key_logging(self):
        """Test enhanced logging in load_google_api_key function."""
        from stages.stage2 import load_google_api_key
        
        with patch('stages.stage2.debug') as mock_debug, \
             patch('os.path.exists') as mock_exists, \
             patch('os.environ.get') as mock_env_get:
            
            mock_exists.return_value = False
            mock_env_get.return_value = ""
            
            # Clear cache first
            load_google_api_key.clear()
            
            result = load_google_api_key()
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage2"' in str(call) for call in calls))
            self.assertTrue(any('operation=' in str(call) for call in calls))
    
    def test_stage2_validate_website_url_logging(self):
        """Test enhanced logging in validate_website_url function."""
        from stages.stage2 import validate_website_url
        
        with patch('stages.stage2.debug') as mock_debug:
            # Test with valid URL
            is_valid, error = validate_website_url("https://test.com")
            self.assertTrue(is_valid)
            
            # Test with invalid URL
            is_valid, error = validate_website_url("")
            self.assertFalse(is_valid)
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage2"' in str(call) for call in calls))
            self.assertTrue(any('operation=' in str(call) for call in calls))


class TestStage3LoggingEnhancements(unittest.TestCase):
    """Test Stage 3 logging enhancements."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        import logging
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_stage3_logger_creation(self):
        """Test that Stage 3 uses centralized logger."""
        logger = get_stage_logger("stage3")
        
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, "gretah.scriptweaver.stage3")
    
    def test_stage3_convert_test_case_entry_logging(self):
        """Test enhanced logging in stage3_convert_test_case function entry."""
        from stages.stage3 import stage3_convert_test_case
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.current_stage.name = "STAGE3_CONVERT"
        mock_state.test_cases = [{'Test Case ID': 'TC001'}]
        
        with patch('stages.stage3.debug') as mock_debug, \
             patch('streamlit.markdown'), \
             patch('streamlit.session_state', {}):
            
            try:
                stage3_convert_test_case(mock_state)
            except:
                # We expect this to fail due to missing dependencies, 
                # but we want to test the logging at the entry point
                pass
            
            # Verify debug was called with structured parameters
            mock_debug.assert_called()
            calls = mock_debug.call_args_list
            self.assertTrue(any('stage="stage3"' in str(call) for call in calls))
            self.assertTrue(any('operation=' in str(call) for call in calls))


class TestPhase3bIntegration(unittest.TestCase):
    """Test integration of Phase 3b enhancements across Stages 1-3."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing logging configuration
        import logging
        logging.getLogger().handlers.clear()
        
        # Reset singleton instances
        GretahLoggingManager._instance = None
        
        # Create temporary directory for test logs
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_all_stages_use_centralized_logging(self):
        """Test that all stages 1-3 use centralized logging."""
        stage1_logger = get_stage_logger("stage1")
        stage2_logger = get_stage_logger("stage2")
        stage3_logger = get_stage_logger("stage3")
        
        # Verify all loggers are created
        self.assertIsNotNone(stage1_logger)
        self.assertIsNotNone(stage2_logger)
        self.assertIsNotNone(stage3_logger)
        
        # Verify correct naming
        self.assertEqual(stage1_logger.name, "gretah.scriptweaver.stage1")
        self.assertEqual(stage2_logger.name, "gretah.scriptweaver.stage2")
        self.assertEqual(stage3_logger.name, "gretah.scriptweaver.stage3")
        
        # Verify they're different instances
        self.assertNotEqual(stage1_logger, stage2_logger)
        self.assertNotEqual(stage2_logger, stage3_logger)
    
    def test_backward_compatibility_maintained(self):
        """Test that existing functionality is not broken."""
        # Test that basic debug calls still work
        try:
            debug("Test message from stage 1")
            debug("Test message from stage 2") 
            debug("Test message from stage 3")
        except Exception as e:
            self.fail(f"Basic debug calls failed: {e}")
    
    def test_enhanced_debug_functionality(self):
        """Test that enhanced debug functionality works across all stages."""
        try:
            # Test enhanced debug calls for each stage
            debug("Enhanced test message", stage="stage1", operation="test")
            debug("Enhanced test message", stage="stage2", operation="test")
            debug("Enhanced test message", stage="stage3", operation="test")
        except Exception as e:
            self.fail(f"Enhanced debug calls failed: {e}")
    
    def test_performance_no_regression(self):
        """Test that logging enhancements don't cause performance regression."""
        import time
        
        # Test performance with enhanced logging disabled
        os.environ['SCRIPTWEAVER_DEBUG'] = 'false'
        
        start_time = time.time()
        
        # Simulate typical Stage 1-3 debug volume
        for stage in ["stage1", "stage2", "stage3"]:
            for i in range(20):  # 20 debug statements per stage
                debug(f"Performance test debug statement {i}", stage=stage, operation="performance_test")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete quickly when debug is disabled
        self.assertLess(execution_time, 0.2, "Performance regression detected in Stages 1-3")


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)
