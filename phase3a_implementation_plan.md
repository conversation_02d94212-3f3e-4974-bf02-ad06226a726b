# Phase 3a: Core Logging Infrastructure Implementation Plan

## **Overview**

Phase 3a establishes the foundational logging infrastructure for GretahAI ScriptWeaver while maintaining 100% backward compatibility with existing functionality, particularly Stage 10's comprehensive logging and the SCRIPTWEAVER_DEBUG environment variable.

## **Implementation Details**

### **1. New Module: `core/logging_config.py`**

**Purpose**: Centralized logging configuration manager that integrates with existing AI logging infrastructure

```python
"""
Centralized logging configuration for GretahAI ScriptWeaver.

This module provides a unified logging infrastructure that:
- Maintains backward compatibility with existing patterns
- Integrates with SCRIPTWEAVER_DEBUG environment variable
- Extends AI logging infrastructure from core/ai_helpers.py
- Provides hierarchical logger management
"""

import os
import logging
import logging.handlers
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# Import existing AI logging infrastructure
from .ai_helpers import LoggingManager as AILoggingManager

class GretahLoggingManager:
    """
    Centralized logging manager for GretahAI ScriptWeaver.
    
    Extends the existing AI logging infrastructure while providing
    standardized configuration for all application components.
    """
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance."""
        if cls._instance is None:
            cls._instance = GretahLoggingManager()
        return cls._instance
    
    def __init__(self):
        # Load configuration from environment
        self.config = self._load_environment_config()
        
        # Initialize logger registry
        self.loggers = {}
        
        # Get existing AI logging manager
        self.ai_logging_manager = AILoggingManager.get_instance()
        
        # Setup root logger and formatters
        self._setup_root_logger()
        self._setup_formatters()
        
        # Create log directories
        self._ensure_log_directories()
    
    def get_stage_logger(self, stage_name: str) -> logging.Logger:
        """
        Get or create a logger for a specific stage.
        
        Args:
            stage_name: Stage identifier (e.g., "stage1", "stage10")
            
        Returns:
            Configured logger instance
        """
        logger_name = f"gretah.scriptweaver.{stage_name}"
        
        if logger_name not in self.loggers:
            logger = logging.getLogger(logger_name)
            self._configure_logger(logger, logger_name)
            self.loggers[logger_name] = logger
        
        return self.loggers[logger_name]
    
    def get_component_logger(self, component_name: str) -> logging.Logger:
        """
        Get or create a logger for a specific component.
        
        Args:
            component_name: Component identifier (e.g., "state_manager", "ui")
            
        Returns:
            Configured logger instance
        """
        logger_name = f"gretah.scriptweaver.{component_name}"
        
        if logger_name not in self.loggers:
            logger = logging.getLogger(logger_name)
            self._configure_logger(logger, logger_name)
            self.loggers[logger_name] = logger
        
        return self.loggers[logger_name]
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """Load logging configuration from environment variables."""
        
        # Check for backward compatibility with SCRIPTWEAVER_DEBUG
        scriptweaver_debug = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")
        
        # New environment variables (take precedence if set)
        log_level = os.environ.get("GRETAH_LOG_LEVEL", "").upper()
        if not log_level:
            log_level = "DEBUG" if scriptweaver_debug else "INFO"
        
        config = {
            'level': getattr(logging, log_level, logging.INFO),
            'console_output': self._parse_bool_env("GRETAH_LOG_CONSOLE", scriptweaver_debug),
            'file_output': self._parse_bool_env("GRETAH_LOG_FILE", True),
            'ui_output': self._parse_bool_env("GRETAH_LOG_UI", False),
            'stage_filter': os.environ.get("GRETAH_LOG_STAGE_FILTER", ""),
            'backward_compatibility': scriptweaver_debug,
            'format_style': os.environ.get("GRETAH_LOG_FORMAT", "structured")
        }
        
        return config
    
    def _parse_bool_env(self, env_var: str, default: bool) -> bool:
        """Parse boolean environment variable."""
        value = os.environ.get(env_var, "").lower()
        if value in ("true", "1", "yes"):
            return True
        elif value in ("false", "0", "no"):
            return False
        return default
    
    def _setup_root_logger(self):
        """Setup the root logger configuration."""
        root_logger = logging.getLogger("gretah.scriptweaver")
        root_logger.setLevel(self.config['level'])
        
        # Prevent duplicate handlers
        if not root_logger.handlers:
            # Add console handler if enabled
            if self.config['console_output']:
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(self.formatters['console'])
                root_logger.addHandler(console_handler)
            
            # Add file handler if enabled
            if self.config['file_output']:
                file_handler = self._create_file_handler()
                file_handler.setFormatter(self.formatters['file'])
                root_logger.addHandler(file_handler)
    
    def _setup_formatters(self):
        """Create standardized log formatters."""
        self.formatters = {
            'console': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
                datefmt='%H:%M:%S'
            ),
            'file': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s [%(funcName)s:%(lineno)d] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            'structured': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s [%(operation)s] - %(message)s [%(context)s]',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        }
    
    def _create_file_handler(self) -> logging.Handler:
        """Create rotating file handler for application logs."""
        log_file = Path("logs") / "gretah_scriptweaver.log"
        
        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        return handler
    
    def _configure_logger(self, logger: logging.Logger, logger_name: str):
        """Configure individual logger with appropriate settings."""
        
        # Set level based on configuration and stage filter
        if self.config['stage_filter']:
            stage_name = logger_name.split('.')[-1]
            if stage_name != self.config['stage_filter']:
                logger.setLevel(logging.WARNING)  # Reduce noise from other stages
            else:
                logger.setLevel(self.config['level'])
        else:
            logger.setLevel(self.config['level'])
        
        # Prevent propagation to avoid duplicate messages
        logger.propagate = True
    
    def _ensure_log_directories(self):
        """Ensure all required log directories exist."""
        directories = [
            "logs",
            "ai_logs",
            "ai_logs/requests", 
            "ai_logs/errors",
            "ai_logs/metrics"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def is_debug_enabled(self) -> bool:
        """Check if debug logging is enabled (backward compatibility)."""
        return self.config['level'] <= logging.DEBUG
    
    def should_output_to_console(self) -> bool:
        """Check if console output is enabled."""
        return self.config['console_output']


# Convenience functions for easy access
def get_stage_logger(stage_name: str) -> logging.Logger:
    """Get a logger for a specific stage."""
    return GretahLoggingManager.get_instance().get_stage_logger(stage_name)

def get_component_logger(component_name: str) -> logging.Logger:
    """Get a logger for a specific component."""
    return GretahLoggingManager.get_instance().get_component_logger(component_name)

def is_debug_enabled() -> bool:
    """Check if debug logging is enabled."""
    return GretahLoggingManager.get_instance().is_debug_enabled()

def should_output_to_console() -> bool:
    """Check if console output is enabled."""
    return GretahLoggingManager.get_instance().should_output_to_console()
```

### **2. Enhanced `debug_utils.py`**

**Purpose**: Maintain backward compatibility while adding structured logging capabilities

**Key Changes:**
- Preserve existing `debug()` function signature
- Add optional structured logging parameters
- Integrate with centralized logging manager
- Maintain SCRIPTWEAVER_DEBUG behavior

```python
"""
Enhanced debug utilities for GretahAI ScriptWeaver.

This module provides backward-compatible debug functionality while
integrating with the new centralized logging infrastructure.
"""

import os
import logging
import streamlit as st
import inspect
from typing import Dict, Any, Optional

# Import new centralized logging
try:
    from core.logging_config import GretahLoggingManager, is_debug_enabled, should_output_to_console
    CENTRALIZED_LOGGING_AVAILABLE = True
except ImportError:
    # Fallback for development/testing
    CENTRALIZED_LOGGING_AVAILABLE = False

# Maintain backward compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.debug_utils")

# Legacy debug mode flag (maintained for compatibility)
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message: str, stage: Optional[str] = None, operation: Optional[str] = None, 
          context: Optional[Dict[str, Any]] = None):
    """
    Enhanced debug function with backward compatibility.
    
    Args:
        message: Debug message (required, maintains compatibility)
        stage: Stage identifier (optional, new feature)
        operation: Operation being performed (optional, new feature)  
        context: Additional context data (optional, new feature)
    """
    
    # Determine the calling stage if not provided
    if not stage:
        stage = _get_calling_stage()
    
    # Use centralized logging if available
    if CENTRALIZED_LOGGING_AVAILABLE:
        try:
            manager = GretahLoggingManager.get_instance()
            stage_logger = manager.get_stage_logger(stage or "unknown")
            
            # Create structured log entry
            if stage_logger.isEnabledFor(logging.DEBUG):
                extra_data = {}
                if operation:
                    extra_data['operation'] = operation
                if context:
                    extra_data['context'] = str(context)
                
                stage_logger.debug(message, extra=extra_data)
            
            # Console output for development (maintains SCRIPTWEAVER_DEBUG behavior)
            if manager.should_output_to_console():
                print(f"DEBUG: {message}")
                
        except Exception as e:
            # Fallback to legacy behavior if centralized logging fails
            _legacy_debug(message)
            return
    else:
        # Legacy behavior when centralized logging not available
        _legacy_debug(message)
    
    # Streamlit UI integration (maintains existing behavior)
    _handle_streamlit_debug(message)

def _legacy_debug(message: str):
    """Legacy debug behavior for backward compatibility."""
    logger.debug(message)
    
    if DEBUG_MODE:
        print(f"DEBUG: {message}")

def _get_calling_stage() -> str:
    """Determine the calling stage from the call stack."""
    try:
        frame = inspect.currentframe()
        while frame:
            filename = frame.f_code.co_filename
            if 'stage' in filename and filename.endswith('.py'):
                # Extract stage name from filename
                stage_name = os.path.basename(filename).replace('.py', '')
                return stage_name
            frame = frame.f_back
        return "unknown"
    except:
        return "unknown"

def _handle_streamlit_debug(message: str):
    """Handle Streamlit UI debug display (maintains existing behavior)."""
    try:
        if st._is_running and (DEBUG_MODE or (CENTRALIZED_LOGGING_AVAILABLE and is_debug_enabled())):
            if 'show_debug' not in st.session_state:
                st.session_state.show_debug = False
                st.session_state.debug_messages = []
            
            st.session_state.debug_messages.append(message)
    except:
        pass

# Maintain existing function for compatibility
def get_debug_mode() -> bool:
    """Get current debug mode status."""
    if CENTRALIZED_LOGGING_AVAILABLE:
        return is_debug_enabled()
    return DEBUG_MODE
```
