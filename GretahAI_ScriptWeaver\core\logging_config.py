"""
Centralized logging configuration for GretahAI ScriptWeaver.

This module provides a unified logging infrastructure that:
- Integrates with SCRIPTWEAVER_DEBUG environment variable
- Extends AI logging infrastructure from core/ai_helpers.py
- Provides hierarchical logger management
- Supports structured logging with operation and context parameters

GRETAH Logging Standard Implementation
"""

import os
import logging
import logging.handlers
from typing import Dict, Any
from pathlib import Path

# Import existing AI logging infrastructure for integration
try:
    from .ai_helpers import LoggingManager as AILoggingManager
    AI_LOGGING_AVAILABLE = True
except ImportError:
    AI_LOGGING_AVAILABLE = False


class GretahLoggingManager:
    """
    Centralized logging manager for GretahAI ScriptWeaver.

    Provides standardized configuration for all application components
    with structured logging support.

    Key Features:
    - SCRIPTWEAVER_DEBUG environment variable support
    - Hierarchical logger naming (gretah.scriptweaver.*)
    - Environment-based configuration
    - Integration with existing AI logging infrastructure
    - Structured logging with operation and context parameters
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance."""
        if cls._instance is None:
            cls._instance = GretahLoggingManager()
        return cls._instance

    def __init__(self):
        # Load configuration from environment
        self.config = self._load_environment_config()

        # Initialize logger registry
        self.loggers = {}

        # Get existing AI logging manager if available
        self.ai_logging_manager = None
        if AI_LOGGING_AVAILABLE:
            try:
                self.ai_logging_manager = AILoggingManager.get_instance()
            except Exception:
                pass

        # Setup root logger and formatters
        self._setup_formatters()
        self._setup_root_logger()

        # Create log directories
        self._ensure_log_directories()
    
    def get_stage_logger(self, stage_name: str) -> logging.Logger:
        """
        Get or create a logger for a specific stage.
        
        Args:
            stage_name: Stage identifier (e.g., "stage1", "stage10")
            
        Returns:
            Configured logger instance
        """
        logger_name = f"gretah.scriptweaver.{stage_name}"
        
        if logger_name not in self.loggers:
            logger = logging.getLogger(logger_name)
            self._configure_logger(logger, logger_name)
            self.loggers[logger_name] = logger
        
        return self.loggers[logger_name]
    
    def get_component_logger(self, component_name: str) -> logging.Logger:
        """
        Get or create a logger for a specific component.
        
        Args:
            component_name: Component identifier (e.g., "state_manager", "ui")
            
        Returns:
            Configured logger instance
        """
        logger_name = f"gretah.scriptweaver.{component_name}"
        
        if logger_name not in self.loggers:
            logger = logging.getLogger(logger_name)
            self._configure_logger(logger, logger_name)
            self.loggers[logger_name] = logger
        
        return self.loggers[logger_name]
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """Load logging configuration from environment variables."""

        # Check SCRIPTWEAVER_DEBUG for compatibility
        scriptweaver_debug = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

        # Environment variables with SCRIPTWEAVER_DEBUG fallback
        log_level = os.environ.get("GRETAH_LOG_LEVEL", "DEBUG" if scriptweaver_debug else "INFO").upper()

        config = {
            'level': getattr(logging, log_level, logging.INFO),
            'console_output': self._parse_bool_env("GRETAH_LOG_CONSOLE", scriptweaver_debug),
            'file_output': self._parse_bool_env("GRETAH_LOG_FILE", True),
            'ui_output': self._parse_bool_env("GRETAH_LOG_UI", scriptweaver_debug),
            'stage_filter': os.environ.get("GRETAH_LOG_STAGE_FILTER", ""),
            'format_style': os.environ.get("GRETAH_LOG_FORMAT", "structured")
        }

        return config
    
    def _parse_bool_env(self, env_var: str, default: bool) -> bool:
        """Parse boolean environment variable."""
        value = os.environ.get(env_var, "").lower()
        if value in ("true", "1", "yes"):
            return True
        elif value in ("false", "0", "no"):
            return False
        return default
    
    def _setup_formatters(self):
        """Create standardized log formatters."""
        self.formatters = {
            'console': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s - %(message)s',
                datefmt='%H:%M:%S'
            ),
            'file': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s [%(funcName)s:%(lineno)d] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            'structured': logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s [%(operation)s] - %(message)s [%(context)s]',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        }
    
    def _setup_root_logger(self):
        """Setup the root logger configuration."""
        root_logger = logging.getLogger("gretah.scriptweaver")
        root_logger.setLevel(self.config['level'])

        # Prevent duplicate handlers
        if not root_logger.handlers:
            # Add console handler if enabled
            if self.config['console_output']:
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(self.formatters['console'])
                root_logger.addHandler(console_handler)

            # Add file handler if enabled
            if self.config['file_output']:
                try:
                    file_handler = self._create_file_handler()
                    file_handler.setFormatter(self.formatters['file'])
                    root_logger.addHandler(file_handler)
                except Exception:
                    # If file handler creation fails, continue without it
                    # This ensures the logging system remains functional
                    pass
    
    def _create_file_handler(self) -> logging.Handler:
        """Create rotating file handler for application logs."""
        # Ensure logs directory exists before creating file handler
        log_dir = Path("logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        log_file = log_dir / "gretah_scriptweaver.log"

        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )

        return handler
    
    def _configure_logger(self, logger: logging.Logger, logger_name: str):
        """Configure individual logger with appropriate settings."""

        # Set level based on configuration and stage filter
        if self.config['stage_filter']:
            stage_name = logger_name.split('.')[-1]
            if stage_name != self.config['stage_filter']:
                logger.setLevel(logging.WARNING)  # Reduce noise from other stages
            else:
                logger.setLevel(self.config['level'])
        else:
            logger.setLevel(self.config['level'])

        # Allow propagation to root logger
        logger.propagate = True
    
    def _ensure_log_directories(self):
        """Ensure all required log directories exist."""
        directories = [
            "logs",
            "ai_logs",
            "ai_logs/requests", 
            "ai_logs/errors",
            "ai_logs/metrics"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def is_debug_enabled(self) -> bool:
        """Check if debug logging is enabled."""
        return self.config['level'] <= logging.DEBUG
    
    def should_output_to_console(self) -> bool:
        """Check if console output is enabled."""
        return self.config['console_output']
    
    def should_output_to_ui(self) -> bool:
        """Check if UI output is enabled."""
        return self.config['ui_output']


# Convenience functions for easy access
def get_stage_logger(stage_name: str) -> logging.Logger:
    """Get a logger for a specific stage."""
    return GretahLoggingManager.get_instance().get_stage_logger(stage_name)

def get_component_logger(component_name: str) -> logging.Logger:
    """Get a logger for a specific component."""
    return GretahLoggingManager.get_instance().get_component_logger(component_name)

def is_debug_enabled() -> bool:
    """Check if debug logging is enabled."""
    return GretahLoggingManager.get_instance().is_debug_enabled()

def should_output_to_console() -> bool:
    """Check if console output is enabled."""
    return GretahLoggingManager.get_instance().should_output_to_console()

def should_output_to_ui() -> bool:
    """Check if UI output is enabled."""
    return GretahLoggingManager.get_instance().should_output_to_ui()
