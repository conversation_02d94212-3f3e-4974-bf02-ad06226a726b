# Before/After Code Examples - Stage 10 Logging Enhancement

## **Overview**

This document demonstrates how the new centralized logging infrastructure enhances Stage 10's existing comprehensive logging while maintaining 100% backward compatibility.

## **Stage 10 Before/After Comparison**

### **Before: Current Stage 10 Implementation**

```python
# stages/stage10.py - Current Implementation
import os
import logging
import streamlit as st
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")

def stage10_script_playground(state):
    """Stage 10: Script Template Manager."""
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    
    debug("Stage 10: Script Playground accessed")
    
    # ... existing code ...

def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode):
    """Execute a generated script with comprehensive logging."""
    try:
        debug(f"Executing generated script: {filename}")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name
        
        debug(f"Created temporary script file: {temp_script_path}")
        
        # Execute script
        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            test_results = _execute_script_with_conftest(
                temp_script_path, filename, target_test_case, verbose_mode
            )
            
            debug(f"Script execution completed and results stored: {filename}")
            
            # Store results
            execution_key = f"stage10_execution_results_{filename}"
            st.session_state[execution_key] = test_results
            
            if test_results.get('success', False):
                st.success(f"✅ **Script executed successfully!**")
            else:
                st.error(f"❌ **Script execution failed.**")
                
    except Exception as e:
        error_msg = f"Failed to execute generated script: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        debug(f"Script execution error: {e}")
    finally:
        # Cleanup
        try:
            if os.path.exists(temp_script_path):
                os.unlink(temp_script_path)
                debug(f"Cleaned up temporary script file: {temp_script_path}")
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary script file: {cleanup_error}")
```

### **After: Enhanced Stage 10 Implementation**

```python
# stages/stage10.py - Enhanced Implementation
import os
import logging
import streamlit as st
from debug_utils import debug
from core.logging_config import get_stage_logger

# Enhanced logging configuration
logger = get_stage_logger("stage10")

def stage10_script_playground(state):
    """Stage 10: Script Template Manager."""
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    
    # Enhanced debug with structured context
    debug("Script Playground accessed", 
          stage="stage10", 
          operation="playground_entry",
          context={'user_session': True, 'templates_available': len(state.script_history)})
    
    # ... existing code ...

def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode):
    """Execute a generated script with comprehensive logging."""
    
    # Enhanced execution context
    execution_context = {
        'filename': filename,
        'test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
        'script_length': len(generated_script),
        'verbose_mode': verbose_mode
    }
    
    try:
        debug(f"Executing generated script: {filename}", 
              stage="stage10", 
              operation="script_execution",
              context=execution_context)
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name
        
        debug(f"Created temporary script file: {temp_script_path}", 
              stage="stage10", 
              operation="temp_file_creation",
              context={'temp_path': temp_script_path, 'file_size': len(generated_script)})
        
        # Execute script with enhanced monitoring
        execution_start = time.time()
        
        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            test_results = _execute_script_with_conftest(
                temp_script_path, filename, target_test_case, verbose_mode
            )
            
            execution_duration = time.time() - execution_start
            
            # Enhanced completion logging
            debug(f"Script execution completed: {filename}", 
                  stage="stage10", 
                  operation="script_execution_complete",
                  context={
                      'duration_seconds': round(execution_duration, 2),
                      'success': test_results.get('success', False),
                      'return_code': test_results.get('return_code', -1),
                      'stdout_length': len(test_results.get('stdout', '')),
                      'stderr_length': len(test_results.get('stderr', ''))
                  })
            
            # Store results
            execution_key = f"stage10_execution_results_{filename}"
            st.session_state[execution_key] = test_results
            
            if test_results.get('success', False):
                st.success(f"✅ **Script executed successfully!**")
                debug("Script execution success notification displayed",
                      stage="stage10", 
                      operation="user_notification",
                      context={'notification_type': 'success'})
            else:
                st.error(f"❌ **Script execution failed.**")
                debug("Script execution failure notification displayed",
                      stage="stage10", 
                      operation="user_notification",
                      context={'notification_type': 'error'})
                
    except Exception as e:
        error_msg = f"Failed to execute generated script: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        
        # Enhanced error logging
        debug(f"Script execution error: {e}", 
              stage="stage10", 
              operation="script_execution_error",
              context={
                  'error_type': type(e).__name__,
                  'error_message': str(e),
                  'filename': filename,
                  'execution_context': execution_context
              })
              
    finally:
        # Enhanced cleanup logging
        cleanup_context = {'temp_path': temp_script_path}
        
        try:
            if os.path.exists(temp_script_path):
                file_size = os.path.getsize(temp_script_path)
                os.unlink(temp_script_path)
                
                debug(f"Cleaned up temporary script file: {temp_script_path}", 
                      stage="stage10", 
                      operation="temp_file_cleanup",
                      context={**cleanup_context, 'file_size': file_size, 'cleanup_success': True})
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary script file: {cleanup_error}", 
                  stage="stage10", 
                  operation="temp_file_cleanup_error",
                  context={**cleanup_context, 'cleanup_error': str(cleanup_error)})
```

## **Key Enhancements Demonstrated**

### **1. Structured Context Information**

**Before:**
```python
debug(f"Executing generated script: {filename}")
```

**After:**
```python
debug(f"Executing generated script: {filename}", 
      stage="stage10", 
      operation="script_execution",
      context={
          'filename': filename,
          'test_case_id': target_test_case.get('Test Case ID', 'Unknown'),
          'script_length': len(generated_script),
          'verbose_mode': verbose_mode
      })
```

### **2. Performance Monitoring**

**Before:**
```python
debug(f"Script execution completed and results stored: {filename}")
```

**After:**
```python
execution_duration = time.time() - execution_start
debug(f"Script execution completed: {filename}", 
      stage="stage10", 
      operation="script_execution_complete",
      context={
          'duration_seconds': round(execution_duration, 2),
          'success': test_results.get('success', False),
          'return_code': test_results.get('return_code', -1)
      })
```

### **3. Enhanced Error Context**

**Before:**
```python
debug(f"Script execution error: {e}")
```

**After:**
```python
debug(f"Script execution error: {e}", 
      stage="stage10", 
      operation="script_execution_error",
      context={
          'error_type': type(e).__name__,
          'error_message': str(e),
          'filename': filename,
          'execution_context': execution_context
      })
```

## **Backward Compatibility Verification**

### **Environment Variable Behavior**

**SCRIPTWEAVER_DEBUG=true (Current Behavior Preserved):**
```bash
# Before and After - Identical Output
DEBUG: Stage 10: Script Playground accessed
DEBUG: Executing generated script: test_login_template_20250127_103015.py
DEBUG: Created temporary script file: /tmp/tmpxyz123.py
DEBUG: Script execution completed and results stored: test_login_template_20250127_103015.py
```

**New Enhanced Output (When GRETAH_LOG_LEVEL=DEBUG):**
```bash
# Enhanced structured output (optional)
2025-01-27 10:30:15 [DEBUG] gretah.scriptweaver.stage10 [playground_entry] - Script Playground accessed [user_session=True, templates_available=5]
2025-01-27 10:30:16 [DEBUG] gretah.scriptweaver.stage10 [script_execution] - Executing generated script: test_login_template_20250127_103015.py [filename=test_login_template_20250127_103015.py, test_case_id=TC001, script_length=1247, verbose_mode=False]
2025-01-27 10:30:18 [DEBUG] gretah.scriptweaver.stage10 [script_execution_complete] - Script execution completed: test_login_template_20250127_103015.py [duration_seconds=2.3, success=True, return_code=0]
```

## **Migration Strategy**

### **Phase 1: Infrastructure Setup**
1. Deploy `core/logging_config.py`
2. Enhance `debug_utils.py` with backward compatibility
3. Verify existing Stage 10 behavior unchanged

### **Phase 2: Gradual Enhancement**
1. Add structured context to existing debug statements
2. Implement performance monitoring
3. Enhance error context

### **Phase 3: Advanced Features**
1. Enable new environment variables
2. Implement log filtering and analysis
3. Add performance dashboards

## **Testing Verification**

### **Regression Test Cases**

1. **SCRIPTWEAVER_DEBUG=true**: Verify identical console output
2. **Stage 10 Functionality**: Ensure all features work unchanged  
3. **Performance**: Measure logging overhead impact
4. **Error Handling**: Verify error scenarios still work
5. **Streamlit Integration**: Confirm UI debug display functions

### **Enhancement Test Cases**

1. **Structured Logging**: Verify new context information
2. **Environment Variables**: Test new configuration options
3. **Performance Monitoring**: Validate timing measurements
4. **Log Filtering**: Test stage-specific filtering
5. **Integration**: Verify centralized logging manager
