# Integration Strategy with Existing SCRIPTWEAVER_DEBUG Environment Variable

## **Overview**

This document outlines the comprehensive integration strategy for the new centralized logging infrastructure with the existing SCRIPTWEAVER_DEBUG environment variable, ensuring 100% backward compatibility while providing enhanced functionality.

## **Current SCRIPTWEAVER_DEBUG Implementation Analysis**

### **Existing Behavior**

**Current Implementation in `debug_utils.py`:**
```python
# Current debug_utils.py
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message):
    logger.debug(message)
    
    if DEBUG_MODE:
        print(f"DEBUG: {message}")
        
        # Streamlit UI integration
        try:
            if st._is_running:
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []
                st.session_state.debug_messages.append(message)
        except:
            pass
```

**Current Usage Patterns:**
- **Stage 10**: 67 debug statements using `debug()` function
- **StateManager**: 281 logging statements using direct logger calls
- **Other Stages**: Mixed usage of `debug()` and direct logging

### **User Expectations**

When users set `SCRIPTWEAVER_DEBUG=true`, they expect:
1. **Console Output**: Debug messages printed to console with "DEBUG: " prefix
2. **Stage 10 Visibility**: Comprehensive logging of Stage 10 operations
3. **Streamlit UI**: Optional debug messages in Streamlit sidebar
4. **No Performance Impact**: When disabled, minimal overhead

## **Enhanced Integration Strategy**

### **1. Backward Compatibility Layer**

**Enhanced `debug_utils.py` Implementation:**
```python
"""
Enhanced debug utilities with backward compatibility.
Maintains existing SCRIPTWEAVER_DEBUG behavior while adding new capabilities.
"""

import os
import logging
import streamlit as st
from typing import Dict, Any, Optional

# Import new centralized logging (with fallback)
try:
    from core.logging_config import GretahLoggingManager, get_stage_logger
    CENTRALIZED_LOGGING_AVAILABLE = True
except ImportError:
    CENTRALIZED_LOGGING_AVAILABLE = False

# Maintain existing configuration for backward compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.debug_utils")

# Legacy DEBUG_MODE (preserved exactly)
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")

def debug(message: str, stage: Optional[str] = None, operation: Optional[str] = None, 
          context: Optional[Dict[str, Any]] = None):
    """
    Enhanced debug function maintaining backward compatibility.
    
    Args:
        message: Debug message (required - maintains existing signature)
        stage: Stage identifier (optional - new feature)
        operation: Operation type (optional - new feature)
        context: Additional context (optional - new feature)
    """
    
    # CRITICAL: Maintain exact existing behavior for backward compatibility
    if CENTRALIZED_LOGGING_AVAILABLE and (stage or operation or context):
        # Use enhanced logging only when new parameters are provided
        _enhanced_debug(message, stage, operation, context)
    else:
        # Use legacy behavior for existing calls
        _legacy_debug(message)
    
    # Maintain existing Streamlit integration (unchanged)
    _handle_streamlit_debug(message)

def _legacy_debug(message: str):
    """Exact replica of existing debug behavior."""
    logger.debug(message)
    
    if DEBUG_MODE:
        print(f"DEBUG: {message}")

def _enhanced_debug(message: str, stage: Optional[str], operation: Optional[str], 
                   context: Optional[Dict[str, Any]]):
    """Enhanced debug with structured logging."""
    try:
        manager = GretahLoggingManager.get_instance()
        
        # Determine stage if not provided
        if not stage:
            stage = _get_calling_stage()
        
        stage_logger = manager.get_stage_logger(stage)
        
        # Log with structured data if debug level enabled
        if stage_logger.isEnabledFor(logging.DEBUG):
            extra_data = {}
            if operation:
                extra_data['operation'] = operation
            if context:
                extra_data['context'] = str(context)
            
            stage_logger.debug(message, extra=extra_data)
        
        # Maintain console output behavior
        if manager.should_output_to_console():
            print(f"DEBUG: {message}")
            
    except Exception:
        # Fallback to legacy behavior on any error
        _legacy_debug(message)

def _handle_streamlit_debug(message: str):
    """Maintain existing Streamlit debug behavior (unchanged)."""
    try:
        if st._is_running:
            # Use existing DEBUG_MODE or new debug setting
            debug_enabled = DEBUG_MODE
            if CENTRALIZED_LOGGING_AVAILABLE:
                manager = GretahLoggingManager.get_instance()
                debug_enabled = debug_enabled or manager.is_debug_enabled()
            
            if debug_enabled:
                if 'show_debug' not in st.session_state:
                    st.session_state.show_debug = False
                    st.session_state.debug_messages = []
                st.session_state.debug_messages.append(message)
    except:
        pass

def _get_calling_stage() -> str:
    """Determine calling stage from stack trace."""
    import inspect
    try:
        frame = inspect.currentframe()
        while frame:
            filename = frame.f_code.co_filename
            if 'stage' in filename and filename.endswith('.py'):
                return os.path.basename(filename).replace('.py', '')
            frame = frame.f_back
        return "unknown"
    except:
        return "unknown"

# Maintain existing function for compatibility
def get_debug_mode() -> bool:
    """Get debug mode status (backward compatible)."""
    if CENTRALIZED_LOGGING_AVAILABLE:
        manager = GretahLoggingManager.get_instance()
        return DEBUG_MODE or manager.is_debug_enabled()
    return DEBUG_MODE
```

### **2. Environment Variable Mapping**

**Backward Compatibility Mapping:**
```python
def _load_environment_config(self) -> Dict[str, Any]:
    """Load configuration with SCRIPTWEAVER_DEBUG compatibility."""
    
    # Check legacy environment variable first
    scriptweaver_debug = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() in ("true", "1", "yes")
    
    # New environment variables (take precedence if explicitly set)
    log_level = os.environ.get("GRETAH_LOG_LEVEL", "").upper()
    console_output = os.environ.get("GRETAH_LOG_CONSOLE", "")
    
    # Backward compatibility logic
    if not log_level:
        log_level = "DEBUG" if scriptweaver_debug else "INFO"
    
    if not console_output:
        console_output = "true" if scriptweaver_debug else "false"
    
    config = {
        'level': getattr(logging, log_level, logging.INFO),
        'console_output': console_output.lower() in ("true", "1", "yes"),
        'file_output': True,  # Always enabled
        'ui_output': scriptweaver_debug,  # UI output follows SCRIPTWEAVER_DEBUG
        'stage_filter': os.environ.get("GRETAH_LOG_STAGE_FILTER", ""),
        'backward_compatibility': scriptweaver_debug
    }
    
    return config
```

### **3. Migration Path for Existing Code**

**Phase 1: No Changes Required**
```python
# Existing Stage 10 code - works unchanged
debug("Stage 10: Script Playground accessed")
debug(f"Execute button clicked for {filename}")
debug(f"Script execution completed: {filename}")
```

**Phase 2: Optional Enhancements**
```python
# Enhanced calls - optional migration
debug("Stage 10: Script Playground accessed", 
      stage="stage10", operation="playground_entry")
debug(f"Execute button clicked for {filename}", 
      stage="stage10", operation="button_click", 
      context={'filename': filename, 'action': 'execute'})
```

**Phase 3: Full Enhancement**
```python
# Comprehensive structured logging
debug(f"Script execution completed: {filename}", 
      stage="stage10", 
      operation="script_execution_complete",
      context={
          'filename': filename,
          'duration': execution_time,
          'success': success_status,
          'test_case_id': test_case_id
      })
```

## **Environment Variable Behavior Matrix**

| SCRIPTWEAVER_DEBUG | GRETAH_LOG_LEVEL | Console Output | File Output | UI Output | Behavior |
|-------------------|------------------|----------------|-------------|-----------|----------|
| `true` | (not set) | ✅ | ✅ | ✅ | Legacy behavior preserved |
| `false` | (not set) | ❌ | ✅ | ❌ | Legacy behavior preserved |
| `true` | `INFO` | ✅ | ✅ | ✅ | Enhanced with INFO level |
| `false` | `DEBUG` | ✅ | ✅ | ❌ | New enhanced debug mode |
| (not set) | `DEBUG` | ✅ | ✅ | ❌ | Pure new configuration |

## **Testing Strategy**

### **Backward Compatibility Tests**

1. **Legacy Environment Variable Test**
   ```bash
   export SCRIPTWEAVER_DEBUG=true
   # Verify: Console output identical to current implementation
   # Verify: Stage 10 debug messages appear as before
   # Verify: Streamlit UI debug panel works
   ```

2. **Legacy Function Signature Test**
   ```python
   # Verify existing calls work unchanged
   debug("Simple message")
   debug(f"Message with variable: {value}")
   ```

3. **Performance Regression Test**
   ```bash
   export SCRIPTWEAVER_DEBUG=false
   # Verify: No performance impact when disabled
   # Verify: No console output when disabled
   ```

### **Enhancement Tests**

1. **New Environment Variables Test**
   ```bash
   export GRETAH_LOG_LEVEL=DEBUG
   export GRETAH_LOG_CONSOLE=true
   # Verify: Enhanced logging works
   # Verify: Structured output appears
   ```

2. **Mixed Usage Test**
   ```python
   # Verify both old and new calls work together
   debug("Legacy call")
   debug("Enhanced call", stage="test", operation="validation")
   ```

## **Rollback Strategy**

### **Immediate Rollback**
If issues arise, the system can immediately fall back to legacy behavior:

1. **Environment Variable**: Set `GRETAH_DISABLE_ENHANCED_LOGGING=true`
2. **Code Fallback**: `CENTRALIZED_LOGGING_AVAILABLE = False`
3. **Import Fallback**: Automatic fallback if new modules fail to import

### **Gradual Rollback**
- Disable enhanced features while maintaining infrastructure
- Revert to legacy debug function implementation
- Remove centralized logging manager

## **Documentation Updates**

### **User Documentation**
- Update environment variable documentation
- Provide migration examples
- Document new logging capabilities

### **Developer Documentation**
- Update coding standards for logging
- Provide structured logging guidelines
- Document performance considerations

## **Success Metrics**

1. **Backward Compatibility**: 100% of existing debug calls work unchanged
2. **Performance**: <5% overhead when enhanced logging disabled
3. **Functionality**: All Stage 10 features work identically
4. **Enhancement Adoption**: Gradual adoption of structured logging
5. **User Satisfaction**: No user-reported regressions
